clear
close all
clc

%% 对比MRR和Ka波段数据的最大有效高度（应用高度过滤）
% MRR: 只使用2500m以下数据
% Ka: 只使用2200m以下数据

fprintf('=== 对比MRR和Ka波段数据最大有效高度 ===\n');
fprintf('过滤条件: MRR ≤ 2500m, Ka ≤ 2200m\n\n');

%% 1. 加载MRR数据（7:00-7:59小时）
mrr_filename = '20250108/20250108_060000.nc';
fprintf('1. 加载MRR数据: %s\n', mrr_filename);

try
    mrr_Za = ncread(mrr_filename, 'Za');
    mrr_range = ncread(mrr_filename, 'range');
    mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    % 应用2500m高度过滤
    mrr_valid_height_idx = mrr_range <= 2500;
    mrr_range_filtered = mrr_range(mrr_valid_height_idx);
    mrr_Za_filtered = mrr_Za(mrr_valid_height_idx, :);
    
    fprintf('   MRR原始高度层数: %d\n', length(mrr_range));
    fprintf('   MRR过滤后高度层数: %d (≤2500m)\n', length(mrr_range_filtered));
    fprintf('   MRR高度范围: %.0f - %.0f m\n', min(mrr_range_filtered), max(mrr_range_filtered));
    fprintf('   MRR时间点数: %d\n', length(mrr_time));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载Shandong Ka波段数据
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
fprintf('\n2. 加载Ka波段数据: %s\n', shandong_filename);

try
    shandong_data = load(shandong_filename);
    
    if isfield(shandong_data, 'obs_time') && isfield(shandong_data, 'range_ka') && isfield(shandong_data, 'ze')
        obs_time = shandong_data.obs_time;
        range_ka = shandong_data.range_ka;
        ze = shandong_data.ze;
        
        % 检查range_ka的单位和范围
        fprintf('   Ka原始高度范围: %.0f - %.0f\n', min(range_ka), max(range_ka));
        
        % 如果range_ka的单位不是米，可能需要转换
        if max(range_ka) < 100  % 可能是km单位
            range_ka = range_ka * 1000;  % 转换为米
            fprintf('   Ka高度已转换为米: %.0f - %.0f m\n', min(range_ka), max(range_ka));
        end
        
        % 应用2200m高度过滤
        ka_valid_height_idx = range_ka <= 2200;
        range_ka_filtered = range_ka(ka_valid_height_idx);
        
        fprintf('   Ka原始高度层数: %d\n', length(range_ka));
        fprintf('   Ka过滤后高度层数: %d (≤2200m)\n', length(range_ka_filtered));
        fprintf('   Ka过滤后高度范围: %.0f - %.0f m\n', min(range_ka_filtered), max(range_ka_filtered));
        
        % 获取Ka波段反射率数据
        if isfield(ze, 'ka')
            ka_data = ze.ka;
            ka_data_filtered = ka_data(ka_valid_height_idx, :);
            fprintf('   Ka数据维度: %d x %d\n', size(ka_data_filtered, 1), size(ka_data_filtered, 2));
        else
            fprintf('   错误: 未找到Ka波段数据\n');
            return
        end
        
    else
        fprintf('   错误: 缺少必要的数据字段\n');
        return
    end
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 3. 时间匹配和分析
fprintf('\n3. 时间匹配分析\n');

% 定义7:00-8:00时间范围
start_time = datenum('2025-01-08 07:00:00', 'yyyy-mm-dd HH:MM:SS');
end_time = datenum('2025-01-08 08:00:00', 'yyyy-mm-dd HH:MM:SS');

% 找到MRR在这个时间范围内的数据
mrr_time_mask = mrr_time >= start_time & mrr_time <= end_time;
mrr_time_hour = mrr_time(mrr_time_mask);
mrr_Za_hour = mrr_Za_filtered(:, mrr_time_mask);

fprintf('   MRR在7:00-8:00时间段的数据点: %d\n', length(mrr_time_hour));

% 找到Ka数据在这个时间范围内的数据
ka_time_mask = obs_time >= start_time & obs_time <= end_time;
ka_time_hour = obs_time(ka_time_mask);
ka_data_hour = ka_data_filtered(:, ka_time_mask);

fprintf('   Ka在7:00-8:00时间段的数据点: %d\n', length(ka_time_hour));

%% 4. 计算每个时间点的最大有效高度
fprintf('\n4. 计算最大有效高度\n');

% MRR最大有效高度计算
mrr_max_heights = zeros(size(mrr_time_hour));
for i = 1:length(mrr_time_hour)
    profile = mrr_Za_hour(:, i);
    valid_data_idx = ~isnan(profile) & profile > -30;  % 有效数据阈值
    if any(valid_data_idx)
        valid_heights = mrr_range_filtered(valid_data_idx);
        mrr_max_heights(i) = max(valid_heights);
    else
        mrr_max_heights(i) = NaN;
    end
end

% Ka最大有效高度计算
ka_max_heights = zeros(size(ka_time_hour));
for i = 1:length(ka_time_hour)
    profile = ka_data_hour(:, i);
    valid_data_idx = ~isnan(profile) & profile > -30;  % 有效数据阈值
    if any(valid_data_idx)
        valid_heights = range_ka_filtered(valid_data_idx);
        ka_max_heights(i) = max(valid_heights);
    else
        ka_max_heights(i) = NaN;
    end
end

% 统计结果
mrr_valid_count = sum(~isnan(mrr_max_heights));
ka_valid_count = sum(~isnan(ka_max_heights));

fprintf('   MRR有效时间点: %d/%d\n', mrr_valid_count, length(mrr_time_hour));
fprintf('   Ka有效时间点: %d/%d\n', ka_valid_count, length(ka_time_hour));

if mrr_valid_count > 0
    fprintf('   MRR最大有效高度统计:\n');
    mrr_valid_data = mrr_max_heights(~isnan(mrr_max_heights));
    fprintf('     平均: %.0f m\n', mean(mrr_valid_data));
    fprintf('     最大: %.0f m\n', max(mrr_valid_data));
    fprintf('     最小: %.0f m\n', min(mrr_valid_data));
    fprintf('     标准差: %.0f m\n', std(mrr_valid_data));
end

if ka_valid_count > 0
    fprintf('   Ka最大有效高度统计:\n');
    ka_valid_data = ka_max_heights(~isnan(ka_max_heights));
    fprintf('     平均: %.0f m\n', mean(ka_valid_data));
    fprintf('     最大: %.0f m\n', max(ka_valid_data));
    fprintf('     最小: %.0f m\n', min(ka_valid_data));
    fprintf('     标准差: %.0f m\n', std(ka_valid_data));
end

%% 5. 时间序列对比可视化
fprintf('\n5. 创建对比图表\n');

figure('Position', [100, 100, 1400, 800]);

% 子图1: MRR最大高度时间序列
subplot(2,2,1)
plot(mrr_time_hour, mrr_max_heights, 'b-', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('最大有效高度 [m]')
xlabel('时间 [UTC]')
title('MRR最大有效高度 (≤2500m)')
grid on
ylim([0 2500])

% 子图2: Ka最大高度时间序列
subplot(2,2,2)
plot(ka_time_hour, ka_max_heights, 'r-', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('最大有效高度 [m]')
xlabel('时间 [UTC]')
title('Ka波段最大有效高度 (≤2200m)')
grid on
ylim([0 2200])

% 子图3: 对比散点图（如果时间点匹配）
subplot(2,2,3)
% 找到最接近的时间点进行对比
if length(mrr_time_hour) > 0 && length(ka_time_hour) > 0
    matched_mrr = [];
    matched_ka = [];
    matched_times = [];
    
    for i = 1:length(mrr_time_hour)
        [min_diff, ka_idx] = min(abs(ka_time_hour - mrr_time_hour(i)));
        if min_diff < 1/24/60  % 1分钟内的匹配
            matched_mrr = [matched_mrr; mrr_max_heights(i)];
            matched_ka = [matched_ka; ka_max_heights(ka_idx)];
            matched_times = [matched_times; mrr_time_hour(i)];
        end
    end
    
    if ~isempty(matched_mrr)
        scatter(matched_mrr, matched_ka, 50, 'filled')
        xlabel('MRR最大高度 [m]')
        ylabel('Ka最大高度 [m]')
        title(sprintf('同时刻最大高度对比 (n=%d)', length(matched_mrr)))
        grid on
        axis equal
        xlim([0 2500])
        ylim([0 2200])
        
        % 添加1:1线
        hold on
        plot([0 min(2500,2200)], [0 min(2500,2200)], 'k--', 'LineWidth', 1)
        
        fprintf('   匹配的同时刻数据点: %d\n', length(matched_mrr));
        if length(matched_mrr) > 1
            valid_pairs = ~isnan(matched_mrr) & ~isnan(matched_ka);
            if sum(valid_pairs) > 1
                correlation = corrcoef(matched_mrr(valid_pairs), matched_ka(valid_pairs));
                fprintf('   相关系数: %.3f\n', correlation(1,2));
            end
        end
    end
end

% 子图4: 高度差异分析
subplot(2,2,4)
if exist('matched_mrr', 'var') && ~isempty(matched_mrr)
    height_diff = matched_mrr - matched_ka;
    plot(matched_times, height_diff, 'g-', 'LineWidth', 2)
    datetick('x', 'HH:MM')
    ylabel('高度差异 (MRR - Ka) [m]')
    xlabel('时间 [UTC]')
    title('最大高度差异时间序列')
    grid on
    
    fprintf('   高度差异统计:\n');
    valid_diff = height_diff(~isnan(height_diff));
    if ~isempty(valid_diff)
        fprintf('     平均差异: %.0f m\n', mean(valid_diff));
        fprintf('     最大差异: %.0f m\n', max(valid_diff));
        fprintf('     最小差异: %.0f m\n', min(valid_diff));
    end
end

sgtitle('MRR vs Ka波段最大有效高度对比 - 2025年1月8日 7:00-8:00 UTC', ...
         'FontSize', 14, 'FontWeight', 'bold')

% 保存图片
print('max_height_comparison_filtered', '-dpng', '-r300')
fprintf('   对比图已保存: max_height_comparison_filtered.png\n');

fprintf('\n=== 分析完成 ===\n');
