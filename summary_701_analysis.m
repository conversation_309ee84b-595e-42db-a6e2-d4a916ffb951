clear
close all
clc

%% Summary Analysis for 7:01 Data Heights
% This script provides a clear summary of grid heights with data at 7:01

fprintf('=== SUMMARY: Grid Heights with Data at 7:01 ===\n\n');

%% 1. MRR Data Analysis
fprintf('1. MRR DATA AT 7:01:\n');
fprintf('   File: 20250108/20250108_060000.nc\n');

% Load MRR data
mrr_filename = '20250108/20250108_060000.nc';
try
    mrr_Za = ncread(mrr_filename, 'Za');
    mrr_Z = ncread(mrr_filename, 'Z');
    mrr_RR = ncread(mrr_filename, 'RR');
    mrr_range = ncread(mrr_filename, 'range');
    mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    % Find 7:01 time index
    target_time = datenum('2025-01-08 07:01:00', 'yyyy-mm-dd HH:MM:SS');
    [~, time_idx] = min(abs(mrr_time - target_time));
    
    % Extract data at 7:01
    mrr_Za_701 = mrr_Za(:, time_idx);
    mrr_Z_701 = mrr_Z(:, time_idx);
    mrr_RR_701 = mrr_RR(:, time_idx);
    
    % Find valid data heights
    valid_Za_idx = ~isnan(mrr_Za_701) & mrr_Za_701 > -30;
    valid_Z_idx = ~isnan(mrr_Z_701) & mrr_Z_701 > -30;
    valid_RR_idx = ~isnan(mrr_RR_701) & mrr_RR_701 > 0.01;
    
    mrr_heights_Za = mrr_range(valid_Za_idx);
    mrr_heights_Z = mrr_range(valid_Z_idx);
    mrr_heights_RR = mrr_range(valid_RR_idx);
    
    fprintf('   - Za (Reflectivity Factor): %d levels with data\n', length(mrr_heights_Za));
    fprintf('     Height range: %.0f - %.0f m\n', min(mrr_heights_Za), max(mrr_heights_Za));
    fprintf('     Heights (m): ');
    for i = 1:min(15, length(mrr_heights_Za))
        fprintf('%.0f ', mrr_heights_Za(i));
    end
    if length(mrr_heights_Za) > 15
        fprintf('... (total %d levels)', length(mrr_heights_Za));
    end
    fprintf('\n');
    
    fprintf('   - Z (Equivalent Reflectivity): %d levels with data\n', length(mrr_heights_Z));
    fprintf('     Height range: %.0f - %.0f m\n', min(mrr_heights_Z), max(mrr_heights_Z));
    
    fprintf('   - RR (Rain Rate): %d levels with data\n', length(mrr_heights_RR));
    fprintf('     Height range: %.0f - %.0f m\n', min(mrr_heights_RR), max(mrr_heights_RR));
    
catch ME
    fprintf('   Error loading MRR data: %s\n', ME.message);
end

%% 2. Shandong Ka and W Band Data Analysis
fprintf('\n2. SHANDONG Ka AND W BAND DATA AT 7:01:\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
if exist(shandong_filename, 'file')
    fprintf('   File: %s\n', shandong_filename);
    
    try
        shandong_data = load(shandong_filename);
        
        % Check the structure
        fprintf('   Available variables: ');
        field_names = fieldnames(shandong_data);
        for i = 1:length(field_names)
            fprintf('%s ', field_names{i});
        end
        fprintf('\n');
        
        % Analyze obs_time and range_ka
        if isfield(shandong_data, 'obs_time') && isfield(shandong_data, 'range_ka')
            obs_time = shandong_data.obs_time;
            range_ka = shandong_data.range_ka;
            
            fprintf('   Time points: %d\n', length(obs_time));
            fprintf('   Ka band height levels: %d\n', length(range_ka));
            fprintf('   Ka band height range: %.0f - %.0f m\n', min(range_ka), max(range_ka));
            
            % Convert obs_time to find 7:01
            % Assuming obs_time is in datenum format or similar
            target_time_shandong = datenum('2025-01-08 07:01:00', 'yyyy-mm-dd HH:MM:SS');
            
            if isnumeric(obs_time)
                [~, shandong_time_idx] = min(abs(obs_time - target_time_shandong));
                time_diff = (obs_time(shandong_time_idx) - target_time_shandong) * 24 * 60;
                fprintf('   Closest time to 7:01: %s (diff: %.1f min)\n', ...
                    datestr(obs_time(shandong_time_idx)), time_diff);
                
                % Check ze structure for Ka and W band data
                if isfield(shandong_data, 'ze')
                    ze = shandong_data.ze;
                    ze_fields = fieldnames(ze);
                    fprintf('   Reflectivity data fields: ');
                    for i = 1:length(ze_fields)
                        fprintf('%s ', ze_fields{i});
                    end
                    fprintf('\n');
                    
                    % Look for Ka and W band data
                    ka_found = false;
                    w_found = false;
                    
                    for i = 1:length(ze_fields)
                        field_name = ze_fields{i};
                        if contains(lower(field_name), 'ka') || contains(lower(field_name), '35')
                            fprintf('   Ka band field found: %s\n', field_name);
                            ka_data = ze.(field_name);
                            if size(ka_data, 2) >= shandong_time_idx
                                ka_701 = ka_data(:, shandong_time_idx);
                                valid_ka = ~isnan(ka_701) & ka_701 > -30;
                                ka_heights = range_ka(valid_ka);
                                fprintf('     Ka band at 7:01: %d levels with data\n', length(ka_heights));
                                if ~isempty(ka_heights)
                                    fprintf('     Ka height range: %.0f - %.0f m\n', min(ka_heights), max(ka_heights));
                                    fprintf('     Ka heights (m): ');
                                    for j = 1:min(15, length(ka_heights))
                                        fprintf('%.0f ', ka_heights(j));
                                    end
                                    if length(ka_heights) > 15
                                        fprintf('... (total %d levels)', length(ka_heights));
                                    end
                                    fprintf('\n');
                                end
                                ka_found = true;
                            end
                        elseif contains(lower(field_name), 'w') || contains(lower(field_name), '94')
                            fprintf('   W band field found: %s\n', field_name);
                            w_data = ze.(field_name);
                            if size(w_data, 2) >= shandong_time_idx
                                w_701 = w_data(:, shandong_time_idx);
                                valid_w = ~isnan(w_701) & w_701 > -30;
                                w_heights = range_ka(valid_w);  % Assuming same height grid
                                fprintf('     W band at 7:01: %d levels with data\n', length(w_heights));
                                if ~isempty(w_heights)
                                    fprintf('     W height range: %.0f - %.0f m\n', min(w_heights), max(w_heights));
                                    fprintf('     W heights (m): ');
                                    for j = 1:min(15, length(w_heights))
                                        fprintf('%.0f ', w_heights(j));
                                    end
                                    if length(w_heights) > 15
                                        fprintf('... (total %d levels)', length(w_heights));
                                    end
                                    fprintf('\n');
                                end
                                w_found = true;
                            end
                        end
                    end
                    
                    if ~ka_found
                        fprintf('   Ka band data not found or not identifiable\n');
                    end
                    if ~w_found
                        fprintf('   W band data not found or not identifiable\n');
                    end
                else
                    fprintf('   No reflectivity (ze) structure found\n');
                end
            else
                fprintf('   Time format not recognized for analysis\n');
            end
        else
            fprintf('   Required fields (obs_time, range_ka) not found\n');
        end
        
    catch ME
        fprintf('   Error loading Shandong data: %s\n', ME.message);
    end
else
    fprintf('   File not found: %s\n', shandong_filename);
    fprintf('   Please check the file path\n');
end

%% 3. Summary Comparison
fprintf('\n3. SUMMARY COMPARISON:\n');
fprintf('   Time analyzed: 7:01 UTC, January 8, 2025\n');
fprintf('   \n');
fprintf('   MRR Data Coverage:\n');
if exist('mrr_heights_Za', 'var')
    fprintf('     - Reflectivity: %.0f - %.0f m (%d levels)\n', ...
        min(mrr_heights_Za), max(mrr_heights_Za), length(mrr_heights_Za));
end
fprintf('   \n');
fprintf('   Shandong Radar Coverage:\n');
if exist('ka_heights', 'var')
    fprintf('     - Ka band: %.0f - %.0f m (%d levels)\n', ...
        min(ka_heights), max(ka_heights), length(ka_heights));
end
if exist('w_heights', 'var')
    fprintf('     - W band: %.0f - %.0f m (%d levels)\n', ...
        min(w_heights), max(w_heights), length(w_heights));
end

fprintf('\n=== Analysis Complete ===\n');
