clear
close all
clc

%% 检查Ka和W波段的高度分辨率

fprintf('=== 检查Ka和W波段高度分辨率 ===\n');

% 加载Shandong数据
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
fprintf('加载数据: %s\n', shandong_filename);

try
    shandong_data = load(shandong_filename);
    
    if isfield(shandong_data, 'range_ka')
        range_ka = shandong_data.range_ka;
        
        fprintf('\n原始range_ka信息:\n');
        fprintf('  数据类型: %s\n', class(range_ka));
        fprintf('  维度: %s\n', mat2str(size(range_ka)));
        fprintf('  原始范围: %.6f - %.6f\n', min(range_ka), max(range_ka));
        
        % 如果是km单位，转换为米
        if max(range_ka) < 100
            range_ka_m = range_ka * 1000;
            fprintf('  转换为米: %.1f - %.1f m\n', min(range_ka_m), max(range_ka_m));
        else
            range_ka_m = range_ka;
        end
        
        % 计算分辨率
        if length(range_ka) > 1
            height_diff = diff(range_ka);
            resolution_original = mean(height_diff);
            resolution_std = std(height_diff);
            
            fprintf('\n高度分辨率分析:\n');
            fprintf('  原始单位分辨率: %.6f (标准差: %.6f)\n', resolution_original, resolution_std);
            
            if max(range_ka) < 100  % 如果是km单位
                resolution_m = resolution_original * 1000;
                fprintf('  米单位分辨率: %.1f m (标准差: %.1f m)\n', resolution_m, resolution_std * 1000);
            else
                fprintf('  米单位分辨率: %.1f m (标准差: %.1f m)\n', resolution_original, resolution_std);
            end
            
            % 检查是否为0.03075km = 30.75m
            expected_res_km = 0.03075;
            expected_res_m = 30.75;
            
            fprintf('\n与预期分辨率对比:\n');
            fprintf('  预期分辨率: %.5f km = %.1f m\n', expected_res_km, expected_res_m);
            
            if max(range_ka) < 100  % km单位
                fprintf('  实际分辨率: %.5f km = %.1f m\n', resolution_original, resolution_original * 1000);
                fprintf('  差异: %.5f km = %.1f m\n', abs(resolution_original - expected_res_km), abs(resolution_original * 1000 - expected_res_m));
            else  % m单位
                fprintf('  实际分辨率: %.5f km = %.1f m\n', resolution_original / 1000, resolution_original);
                fprintf('  差异: %.5f km = %.1f m\n', abs(resolution_original / 1000 - expected_res_km), abs(resolution_original - expected_res_m));
            end
            
            % 显示前10个高度值
            fprintf('\n前10个高度值:\n');
            for i = 1:min(10, length(range_ka))
                if max(range_ka) < 100
                    fprintf('  层%d: %.5f km = %.1f m\n', i, range_ka(i), range_ka(i) * 1000);
                else
                    fprintf('  层%d: %.5f km = %.1f m\n', i, range_ka(i) / 1000, range_ka(i));
                end
            end
            
        else
            fprintf('  只有一个高度值，无法计算分辨率\n');
        end
        
    else
        fprintf('错误: 未找到range_ka字段\n');
    end
    
catch ME
    fprintf('错误: %s\n', ME.message);
end

fprintf('\n=== 分辨率检查完成 ===\n');
