clear
close all
clc

%% 比较MRR、Ka和W波段在20:01时的高度数据
% 应用高度过滤: MRR ≤ 2500m, Ka ≤ 2200m, W ≤ 2200m

fprintf('=== 20:01时刻三种雷达数据高度对比 ===\n');
fprintf('过滤条件: MRR ≤ 2500m, Ka ≤ 2200m, W ≤ 2200m\n\n');

%% 1. 加载MRR数据（20:00-20:59小时）
mrr_filename = '20250108/20250108_200000.nc';
fprintf('1. 加载MRR数据: %s\n', mrr_filename);

if ~exist(mrr_filename, 'file')
    fprintf('   错误: MRR文件不存在\n');
    return
end

try
    mrr_Za = ncread(mrr_filename, 'Za');
    mrr_Z = ncread(mrr_filename, 'Z');
    mrr_RR = ncread(mrr_filename, 'RR');
    mrr_range = ncread(mrr_filename, 'range');
    mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    % 应用2500m高度过滤
    mrr_valid_height_idx = mrr_range <= 2500;
    mrr_range_filtered = mrr_range(mrr_valid_height_idx);
    mrr_Za_filtered = mrr_Za(mrr_valid_height_idx, :);
    mrr_Z_filtered = mrr_Z(mrr_valid_height_idx, :);
    mrr_RR_filtered = mrr_RR(mrr_valid_height_idx, :);
    
    fprintf('   MRR原始高度层数: %d\n', length(mrr_range));
    fprintf('   MRR过滤后高度层数: %d (≤2500m)\n', length(mrr_range_filtered));
    fprintf('   MRR高度范围: %.0f - %.0f m\n', min(mrr_range_filtered), max(mrr_range_filtered));
    fprintf('   MRR时间点数: %d\n', length(mrr_time));
    fprintf('   MRR时间范围: %s 到 %s\n', datestr(min(mrr_time)), datestr(max(mrr_time)));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载Shandong Ka和W波段数据
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
fprintf('\n2. 加载Ka和W波段数据: %s\n', shandong_filename);

if ~exist(shandong_filename, 'file')
    fprintf('   错误: Shandong文件不存在\n');
    return
end

try
    shandong_data = load(shandong_filename);
    
    if isfield(shandong_data, 'obs_time') && isfield(shandong_data, 'range_ka') && isfield(shandong_data, 'ze')
        obs_time = shandong_data.obs_time;
        range_ka = shandong_data.range_ka;
        ze = shandong_data.ze;
        
        % 检查并转换高度单位
        if max(range_ka) < 100  % 可能是km单位
            range_ka = range_ka * 1000;  % 转换为米
            fprintf('   Ka高度已转换为米: %.0f - %.0f m\n', min(range_ka), max(range_ka));
        end
        
        % 应用2200m高度过滤
        ka_valid_height_idx = range_ka <= 2200;
        range_ka_filtered = range_ka(ka_valid_height_idx);
        
        fprintf('   Ka原始高度层数: %d\n', length(range_ka));
        fprintf('   Ka过滤后高度层数: %d (≤2200m)\n', length(range_ka_filtered));
        fprintf('   Ka过滤后高度范围: %.0f - %.0f m\n', min(range_ka_filtered), max(range_ka_filtered));
        
        % 获取Ka和W波段反射率数据
        if isfield(ze, 'ka') && isfield(ze, 'w')
            ka_data = ze.ka;
            w_data = ze.w;
            ka_data_filtered = ka_data(ka_valid_height_idx, :);
            w_data_filtered = w_data(ka_valid_height_idx, :);
            
            fprintf('   Ka数据维度: %d x %d\n', size(ka_data_filtered));
            fprintf('   W数据维度: %d x %d\n', size(w_data_filtered));
            fprintf('   时间点数: %d\n', length(obs_time));
        else
            fprintf('   错误: 未找到Ka或W波段数据\n');
            return
        end
        
    else
        fprintf('   错误: 缺少必要的数据字段\n');
        return
    end
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 3. 找到20:01时刻的数据
fprintf('\n3. 查找20:01时刻数据\n');

% 定义目标时间
target_time = datenum('2025-01-08 20:01:00', 'yyyy-mm-dd HH:MM:SS');
fprintf('   目标时间: %s\n', datestr(target_time));

% 找到MRR最接近的时间点
[mrr_time_diff, mrr_time_idx] = min(abs(mrr_time - target_time));
mrr_actual_time = mrr_time(mrr_time_idx);
fprintf('   MRR最接近时间: %s (差异: %.1f分钟)\n', ...
    datestr(mrr_actual_time), mrr_time_diff * 24 * 60);

% 找到Ka/W最接近的时间点
[ka_time_diff, ka_time_idx] = min(abs(obs_time - target_time));
ka_actual_time = obs_time(ka_time_idx);
fprintf('   Ka/W最接近时间: %s (差异: %.1f分钟)\n', ...
    datestr(ka_actual_time), ka_time_diff * 24 * 60);

%% 4. 提取20:01时刻的垂直剖面数据
fprintf('\n4. 提取垂直剖面数据\n');

% MRR数据
mrr_Za_2001 = mrr_Za_filtered(:, mrr_time_idx);
mrr_Z_2001 = mrr_Z_filtered(:, mrr_time_idx);
mrr_RR_2001 = mrr_RR_filtered(:, mrr_time_idx);

% Ka和W数据
ka_2001 = ka_data_filtered(:, ka_time_idx);
w_2001 = w_data_filtered(:, ka_time_idx);

% 计算有效数据高度
mrr_valid_Za = ~isnan(mrr_Za_2001) & mrr_Za_2001 > -30;
mrr_valid_Z = ~isnan(mrr_Z_2001) & mrr_Z_2001 > -30;
mrr_valid_RR = ~isnan(mrr_RR_2001) & mrr_RR_2001 > 0.01;

ka_valid = ~isnan(ka_2001) & ka_2001 > -30;
w_valid = ~isnan(w_2001) & w_2001 > -30;

% 统计有效高度
mrr_heights_Za = mrr_range_filtered(mrr_valid_Za);
mrr_heights_Z = mrr_range_filtered(mrr_valid_Z);
mrr_heights_RR = mrr_range_filtered(mrr_valid_RR);
ka_heights = range_ka_filtered(ka_valid);
w_heights = range_ka_filtered(w_valid);

fprintf('   MRR Za有效高度层数: %d\n', length(mrr_heights_Za));
if ~isempty(mrr_heights_Za)
    fprintf('     高度范围: %.0f - %.0f m\n', min(mrr_heights_Za), max(mrr_heights_Za));
    fprintf('     最大有效高度: %.0f m\n', max(mrr_heights_Za));
end

fprintf('   MRR Z有效高度层数: %d\n', length(mrr_heights_Z));
if ~isempty(mrr_heights_Z)
    fprintf('     高度范围: %.0f - %.0f m\n', min(mrr_heights_Z), max(mrr_heights_Z));
    fprintf('     最大有效高度: %.0f m\n', max(mrr_heights_Z));
end

fprintf('   MRR RR有效高度层数: %d\n', length(mrr_heights_RR));
if ~isempty(mrr_heights_RR)
    fprintf('     高度范围: %.0f - %.0f m\n', min(mrr_heights_RR), max(mrr_heights_RR));
    fprintf('     最大有效高度: %.0f m\n', max(mrr_heights_RR));
end

fprintf('   Ka波段有效高度层数: %d\n', length(ka_heights));
if ~isempty(ka_heights)
    fprintf('     高度范围: %.0f - %.0f m\n', min(ka_heights), max(ka_heights));
    fprintf('     最大有效高度: %.0f m\n', max(ka_heights));
end

fprintf('   W波段有效高度层数: %d\n', length(w_heights));
if ~isempty(w_heights)
    fprintf('     高度范围: %.0f - %.0f m\n', min(w_heights), max(w_heights));
    fprintf('     最大有效高度: %.0f m\n', max(w_heights));
end

%% 5. 创建20:01时刻对比可视化
fprintf('\n5. 创建20:01时刻对比图表\n');

figure('Position', [100, 100, 1400, 1000]);

% 子图1: MRR垂直剖面
subplot(2,3,1)
plot(mrr_Za_2001, mrr_range_filtered/1000, 'b-', 'LineWidth', 2, 'DisplayName', 'Za')
hold on
plot(mrr_Z_2001, mrr_range_filtered/1000, 'r--', 'LineWidth', 2, 'DisplayName', 'Z')
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('MRR垂直剖面 20:01')
legend('Location', 'best')
grid on
ylim([0 2.5])
xlim([-40 20])

% 子图2: Ka波段垂直剖面
subplot(2,3,2)
plot(ka_2001, range_ka_filtered/1000, 'g-', 'LineWidth', 2)
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('Ka波段垂直剖面 20:01')
grid on
ylim([0 2.2])
xlim([-40 20])

% 子图3: W波段垂直剖面
subplot(2,3,3)
plot(w_2001, range_ka_filtered/1000, 'm-', 'LineWidth', 2)
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('W波段垂直剖面 20:01')
grid on
ylim([0 2.2])
xlim([-40 20])

% 子图4: 数据可用性对比
subplot(2,3,4)
plot(double(mrr_valid_Za), mrr_range_filtered/1000, 'b-', 'LineWidth', 2, 'DisplayName', 'MRR Za')
hold on
plot(double(mrr_valid_Z), mrr_range_filtered/1000, 'r--', 'LineWidth', 2, 'DisplayName', 'MRR Z')
ylabel('高度 [km]')
xlabel('数据可用性 (1=有效, 0=无效)')
title('MRR数据可用性 20:01')
legend('Location', 'best')
grid on
ylim([0 2.5])

% 子图5: Ka数据可用性
subplot(2,3,5)
plot(double(ka_valid), range_ka_filtered/1000, 'g-', 'LineWidth', 2)
ylabel('高度 [km]')
xlabel('数据可用性 (1=有效, 0=无效)')
title('Ka波段数据可用性 20:01')
grid on
ylim([0 2.2])

% 子图6: W数据可用性
subplot(2,3,6)
plot(double(w_valid), range_ka_filtered/1000, 'm-', 'LineWidth', 2)
ylabel('高度 [km]')
xlabel('数据可用性 (1=有效, 0=无效)')
title('W波段数据可用性 20:01')
grid on
ylim([0 2.2])

sgtitle('三种雷达数据在20:01时刻的对比 - 2025年1月8日', 'FontSize', 14, 'FontWeight', 'bold')

% 保存图片
print('radar_comparison_2001', '-dpng', '-r300')
fprintf('   对比图已保存: radar_comparison_2001.png\n');

%% 6. 详细统计对比
fprintf('\n6. 20:01时刻详细统计对比\n');

fprintf('=== MRR数据 ===\n');
fprintf('Za (反射率因子):\n');
fprintf('  有效高度层数: %d\n', sum(mrr_valid_Za));
if sum(mrr_valid_Za) > 0
    fprintf('  最大有效高度: %.0f m\n', max(mrr_heights_Za));
    fprintf('  最小有效高度: %.0f m\n', min(mrr_heights_Za));
    fprintf('  平均反射率: %.1f dBZ\n', mean(mrr_Za_2001(mrr_valid_Za)));
    fprintf('  最大反射率: %.1f dBZ\n', max(mrr_Za_2001(mrr_valid_Za)));
end

fprintf('Z (等效反射率因子):\n');
fprintf('  有效高度层数: %d\n', sum(mrr_valid_Z));
if sum(mrr_valid_Z) > 0
    fprintf('  最大有效高度: %.0f m\n', max(mrr_heights_Z));
    fprintf('  最小有效高度: %.0f m\n', min(mrr_heights_Z));
    fprintf('  平均反射率: %.1f dBZ\n', mean(mrr_Z_2001(mrr_valid_Z)));
    fprintf('  最大反射率: %.1f dBZ\n', max(mrr_Z_2001(mrr_valid_Z)));
end

fprintf('RR (降雨率):\n');
fprintf('  有效高度层数: %d\n', sum(mrr_valid_RR));
if sum(mrr_valid_RR) > 0
    fprintf('  最大有效高度: %.0f m\n', max(mrr_heights_RR));
    fprintf('  最小有效高度: %.0f m\n', min(mrr_heights_RR));
    fprintf('  平均降雨率: %.3f mm/h\n', mean(mrr_RR_2001(mrr_valid_RR)));
    fprintf('  最大降雨率: %.3f mm/h\n', max(mrr_RR_2001(mrr_valid_RR)));
end

fprintf('\n=== Ka波段数据 ===\n');
fprintf('  有效高度层数: %d\n', sum(ka_valid));
if sum(ka_valid) > 0
    fprintf('  最大有效高度: %.0f m\n', max(ka_heights));
    fprintf('  最小有效高度: %.0f m\n', min(ka_heights));
    fprintf('  平均反射率: %.1f dBZ\n', mean(ka_2001(ka_valid)));
    fprintf('  最大反射率: %.1f dBZ\n', max(ka_2001(ka_valid)));
end

fprintf('\n=== W波段数据 ===\n');
fprintf('  有效高度层数: %d\n', sum(w_valid));
if sum(w_valid) > 0
    fprintf('  最大有效高度: %.0f m\n', max(w_heights));
    fprintf('  最小有效高度: %.0f m\n', min(w_heights));
    fprintf('  平均反射率: %.1f dBZ\n', mean(w_2001(w_valid)));
    fprintf('  最大反射率: %.1f dBZ\n', max(w_2001(w_valid)));
end

%% 7. 最大高度对比总结
fprintf('\n=== 20:01时刻最大有效高度总结 ===\n');

max_heights = [];
radar_names = {};

if sum(mrr_valid_Za) > 0
    max_heights(end+1) = max(mrr_heights_Za);
    radar_names{end+1} = 'MRR Za';
end

if sum(mrr_valid_Z) > 0
    max_heights(end+1) = max(mrr_heights_Z);
    radar_names{end+1} = 'MRR Z';
end

if sum(ka_valid) > 0
    max_heights(end+1) = max(ka_heights);
    radar_names{end+1} = 'Ka波段';
end

if sum(w_valid) > 0
    max_heights(end+1) = max(w_heights);
    radar_names{end+1} = 'W波段';
end

% 排序显示
[sorted_heights, sort_idx] = sort(max_heights, 'descend');
sorted_names = radar_names(sort_idx);

fprintf('按最大有效高度排序:\n');
for i = 1:length(sorted_heights)
    fprintf('  %d. %s: %.0f m\n', i, sorted_names{i}, sorted_heights(i));
end

% 创建最大高度对比柱状图
figure('Position', [100, 100, 800, 600]);
bar(max_heights)
set(gca, 'XTickLabel', radar_names)
ylabel('最大有效高度 [m]')
title('20:01时刻各雷达最大有效高度对比')
grid on
xtickangle(45)

% 在柱状图上添加数值标签
for i = 1:length(max_heights)
    text(i, max_heights(i) + 50, sprintf('%.0f m', max_heights(i)), ...
        'HorizontalAlignment', 'center', 'FontSize', 12, 'FontWeight', 'bold')
end

print('max_heights_bar_2001', '-dpng', '-r300')
fprintf('\n柱状图已保存: max_heights_bar_2001.png\n');

fprintf('\n=== 20:01时刻分析完成 ===\n');
