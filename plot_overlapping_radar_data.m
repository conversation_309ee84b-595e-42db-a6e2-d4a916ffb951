clear
close all
clc

%% 绘制MRR和云雷达重合时间段的对比图
% MRR: UTC时间
% 云雷达: BJT时间 (UTC+8)
% 包括反射率因子和速度对比

fprintf('=== 绘制MRR和云雷达重合时间段对比图 ===\n');

%% 1. 加载云雷达数据并修正时区
fprintf('1. 加载云雷达数据\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;
    obs_time_utc = obs_time_bjt - 8/24;  % BJT转UTC
    
    range_ka = shandong_data.range_ka;
    if max(range_ka) < 100
        range_ka = range_ka * 1000;  % 转换为米
    end
    
    % 获取反射率和速度数据
    ka_ze = shandong_data.ze.ka;
    w_ze = shandong_data.ze.w;
    
    % 检查是否有速度数据
    if isfield(shandong_data, 'vel')
        ka_vel = shandong_data.vel.ka;
        w_vel = shandong_data.vel.w;
        has_velocity = true;
        fprintf('   找到速度数据\n');
    else
        has_velocity = false;
        fprintf('   未找到速度数据字段\n');
    end
    
    fprintf('   云雷达UTC时间范围: %s - %s\n', ...
        datestr(min(obs_time_utc)), datestr(max(obs_time_utc)));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载MRR数据并找到重合时间段
fprintf('\n2. 查找重合时间段\n');

% 云雷达UTC时间范围
radar_start_utc = min(obs_time_utc);
radar_end_utc = max(obs_time_utc);

fprintf('   云雷达UTC范围: %s - %s\n', datestr(radar_start_utc), datestr(radar_end_utc));

% 查找包含重合时间的MRR文件
mrr_files = dir('20250108/*.nc');
overlapping_mrr_data = [];
overlapping_mrr_time = [];
overlapping_mrr_range = [];

for i = 1:length(mrr_files)
    mrr_filename = fullfile('20250108', mrr_files(i).name);
    try
        mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        % 检查是否与云雷达时间重合
        if max(mrr_time) >= radar_start_utc && min(mrr_time) <= radar_end_utc
            fprintf('   重合文件: %s\n', mrr_files(i).name);
            
            % 读取数据
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_VEL = ncread(mrr_filename, 'VEL');  % 读取速度数据
            mrr_range = ncread(mrr_filename, 'range');
            
            % 找到重合的时间段
            overlap_mask = mrr_time >= radar_start_utc & mrr_time <= radar_end_utc;
            
            if any(overlap_mask)
                if isempty(overlapping_mrr_data)
                    overlapping_mrr_data = mrr_Za(:, overlap_mask);
                    overlapping_mrr_vel = mrr_VEL(:, overlap_mask);  % 添加速度数据
                    overlapping_mrr_time = mrr_time(overlap_mask);
                    overlapping_mrr_range = mrr_range;
                else
                    overlapping_mrr_data = [overlapping_mrr_data, mrr_Za(:, overlap_mask)];
                    overlapping_mrr_vel = [overlapping_mrr_vel, mrr_VEL(:, overlap_mask)];
                    overlapping_mrr_time = [overlapping_mrr_time; mrr_time(overlap_mask)];
                end
            end
        end
    catch ME
        continue;
    end
end

if isempty(overlapping_mrr_data)
    fprintf('   未找到重合的MRR数据\n');
    return
end

fprintf('   MRR重合时间范围: %s - %s\n', ...
    datestr(min(overlapping_mrr_time)), datestr(max(overlapping_mrr_time)));
fprintf('   重合时间长度: %.1f小时\n', ...
    (max(overlapping_mrr_time) - min(overlapping_mrr_time)) * 24);

%% 3. 提取重合时间段的云雷达数据
overlap_start = max(radar_start_utc, min(overlapping_mrr_time));
overlap_end = min(radar_end_utc, max(overlapping_mrr_time));

radar_overlap_mask = obs_time_utc >= overlap_start & obs_time_utc <= overlap_end;
radar_overlap_time = obs_time_utc(radar_overlap_mask);
ka_ze_overlap = ka_ze(:, radar_overlap_mask);
w_ze_overlap = w_ze(:, radar_overlap_mask);

if has_velocity
    ka_vel_overlap = ka_vel(:, radar_overlap_mask);
    w_vel_overlap = w_vel(:, radar_overlap_mask);
end

fprintf('   云雷达重合数据点: %d\n', sum(radar_overlap_mask));

%% 4. 应用高度过滤
mrr_height_mask = overlapping_mrr_range <= 2500;
radar_height_mask = range_ka <= 2200;

mrr_range_filtered = overlapping_mrr_range(mrr_height_mask);
mrr_data_filtered = overlapping_mrr_data(mrr_height_mask, :);
mrr_vel_filtered = overlapping_mrr_vel(mrr_height_mask, :);  % MRR速度数据

range_ka_filtered = range_ka(radar_height_mask);
ka_ze_filtered = ka_ze_overlap(radar_height_mask, :);
w_ze_filtered = w_ze_overlap(radar_height_mask, :);

if has_velocity
    ka_vel_filtered = ka_vel_overlap(radar_height_mask, :);
    w_vel_filtered = w_vel_overlap(radar_height_mask, :);
end

%% 5. 创建反射率因子对比图
fprintf('\n3. 创建反射率因子对比图\n');

figure('Position', [100, 100, 1400, 1000]);

% 子图1: MRR反射率因子
subplot(3,1,1)
pcolor(overlapping_mrr_time, mrr_range_filtered/1000, mrr_data_filtered)
shading flat
colorbar
caxis([-20 20])
ylim([0 2.5])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
title('MRR Reflectivity Factor (dBZ) - UTC Time')
set(gca, 'FontSize', 12)

% 子图2: Ka波段反射率因子
subplot(3,1,2)
pcolor(radar_overlap_time, range_ka_filtered/1000, ka_ze_filtered)
shading flat
colorbar
caxis([-20 20])
ylim([0 2.2])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
title('Ka-band Reflectivity Factor (dBZ) - UTC Time (converted from BJT)')
set(gca, 'FontSize', 12)

% 子图3: W波段反射率因子
subplot(3,1,3)
pcolor(radar_overlap_time, range_ka_filtered/1000, w_ze_filtered)
shading flat
colorbar
caxis([-20 20])
ylim([0 2.2])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
title('W-band Reflectivity Factor (dBZ) - UTC Time (converted from BJT)')
set(gca, 'FontSize', 12)

sgtitle(sprintf('Radar Reflectivity Comparison - Overlapping Period\n%s to %s UTC', ...
    datestr(overlap_start, 'yyyy-mm-dd HH:MM'), datestr(overlap_end, 'yyyy-mm-dd HH:MM')), ...
    'FontSize', 14, 'FontWeight', 'bold')

% 保存反射率对比图
print('radar_reflectivity_comparison', '-dpng', '-r300')
fprintf('   反射率对比图已保存: radar_reflectivity_comparison.png\n');

%% 6. 创建速度对比图（包含MRR速度）
fprintf('\n4. 创建速度对比图\n');

figure('Position', [100, 100, 1400, 1200]);

% 子图1: MRR速度
subplot(3,1,1)
pcolor(overlapping_mrr_time, mrr_range_filtered/1000, mrr_vel_filtered)
shading flat
clb1 = colorbar;
caxis([0 12])  % MRR速度范围0-12 m/s
ylim([0 2.5])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
ylabel(clb1, 'Velocity [m/s]')
title('MRR Radial Velocity - UTC Time')
set(gca, 'FontSize', 12)

if has_velocity
    % 子图2: Ka波段速度
    subplot(3,1,2)
    pcolor(radar_overlap_time, range_ka_filtered/1000, ka_vel_filtered)
    shading flat
    clb2 = colorbar;
    caxis([-5 5])
    ylim([0 2.2])
    datetick('x', 'HH:MM')
    ylabel('Height [km]')
    xlabel('Time [UTC]')
    ylabel(clb2, 'Velocity [m/s]')
    title('Ka-band Doppler Velocity - UTC Time (converted from BJT)')
    set(gca, 'FontSize', 12)

    % 子图3: W波段速度
    subplot(3,1,3)
    pcolor(radar_overlap_time, range_ka_filtered/1000, w_vel_filtered)
    shading flat
    clb3 = colorbar;
    caxis([-5 5])
    ylim([0 2.2])
    datetick('x', 'HH:MM')
    ylabel('Height [km]')
    xlabel('Time [UTC]')
    ylabel(clb3, 'Velocity [m/s]')
    title('W-band Doppler Velocity - UTC Time (converted from BJT)')
    set(gca, 'FontSize', 12)
else
    fprintf('   云雷达无速度数据\n');
end

sgtitle(sprintf('Radar Velocity Comparison - Overlapping Period\n%s to %s UTC', ...
    datestr(overlap_start, 'yyyy-mm-dd HH:MM'), datestr(overlap_end, 'yyyy-mm-dd HH:MM')), ...
    'FontSize', 14, 'FontWeight', 'bold')

% 保存速度对比图
print('radar_velocity_comparison_3bands', '-dpng', '-r300')
fprintf('   三波段速度对比图已保存: radar_velocity_comparison_3bands.png\n');

%% 7. 创建统计对比
fprintf('\n5. 创建统计对比\n');

% 计算重合时间段的统计信息
mrr_valid = ~isnan(mrr_data_filtered) & mrr_data_filtered > -30;
ka_valid = ~isnan(ka_ze_filtered) & ka_ze_filtered > -30;
w_valid = ~isnan(w_ze_filtered) & w_ze_filtered > -30;

fprintf('重合时间段统计:\n');
fprintf('  时间范围: %s - %s UTC\n', datestr(overlap_start), datestr(overlap_end));
fprintf('  持续时间: %.1f小时\n', (overlap_end - overlap_start) * 24);
fprintf('  MRR数据点: %d\n', size(mrr_data_filtered, 2));
fprintf('  云雷达数据点: %d\n', size(ka_ze_filtered, 2));

fprintf('\n有效数据覆盖:\n');
fprintf('  MRR: %.1f%% (高度0-%.1fkm)\n', ...
    sum(mrr_valid(:))/numel(mrr_valid)*100, max(mrr_range_filtered)/1000);
fprintf('  Ka波段: %.1f%% (高度0-%.1fkm)\n', ...
    sum(ka_valid(:))/numel(ka_valid)*100, max(range_ka_filtered)/1000);
fprintf('  W波段: %.1f%% (高度0-%.1fkm)\n', ...
    sum(w_valid(:))/numel(w_valid)*100, max(range_ka_filtered)/1000);

% 创建数据覆盖对比图
figure('Position', [100, 100, 1200, 400]);

subplot(1,3,1)
imagesc(overlapping_mrr_time, mrr_range_filtered/1000, double(mrr_valid))
colormap(gca, [1 1 1; 0 0 1])  % 白色=无数据，蓝色=有数据
ylim([0 2.5])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
title('MRR Data Coverage')
set(gca, 'FontSize', 10)

subplot(1,3,2)
imagesc(radar_overlap_time, range_ka_filtered/1000, double(ka_valid))
colormap(gca, [1 1 1; 0 1 0])  % 白色=无数据，绿色=有数据
ylim([0 2.2])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
title('Ka-band Data Coverage')
set(gca, 'FontSize', 10)

subplot(1,3,3)
imagesc(radar_overlap_time, range_ka_filtered/1000, double(w_valid))
colormap(gca, [1 1 1; 1 0 1])  % 白色=无数据，紫色=有数据
ylim([0 2.2])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
title('W-band Data Coverage')
set(gca, 'FontSize', 10)

sgtitle('Data Coverage Comparison - Overlapping Period', 'FontSize', 14, 'FontWeight', 'bold')

% 保存数据覆盖图
print('radar_data_coverage', '-dpng', '-r300')
fprintf('   数据覆盖图已保存: radar_data_coverage.png\n');

fprintf('\n=== 对比图生成完成 ===\n');
fprintf('生成的图片:\n');
fprintf('  1. radar_reflectivity_comparison.png - 反射率因子对比\n');
if has_velocity
    fprintf('  2. radar_velocity_comparison.png - 速度对比\n');
end
fprintf('  3. radar_data_coverage.png - 数据覆盖对比\n');
