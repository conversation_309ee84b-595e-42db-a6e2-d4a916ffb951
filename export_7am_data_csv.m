clear
close all
clc

%% 导出7点时刻的MRR、Ka和W波段数据为CSV文件
% 格式: 第一行是时间，第一列是高度，中间是反射率数值

fprintf('=== 导出7点时刻雷达数据为CSV文件 ===\n');

%% 1. 加载MRR数据（7:00-7:59小时）
mrr_filename = '20250108/20250108_060000.nc';
fprintf('1. 加载MRR数据: %s\n', mrr_filename);

try
    mrr_Za = ncread(mrr_filename, 'Za');
    mrr_Z = ncread(mrr_filename, 'Z');
    mrr_range = ncread(mrr_filename, 'range');
    mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    fprintf('   MRR高度层数: %d\n', length(mrr_range));
    fprintf('   MRR高度范围: %.0f - %.0f m\n', min(mrr_range), max(mrr_range));
    fprintf('   MRR时间点数: %d\n', length(mrr_time));
    fprintf('   MRR时间范围: %s 到 %s\n', datestr(min(mrr_time)), datestr(max(mrr_time)));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载Ka和W波段数据
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
fprintf('\n2. 加载Ka和W波段数据: %s\n', shandong_filename);

try
    shandong_data = load(shandong_filename);
    obs_time = shandong_data.obs_time;
    range_ka = shandong_data.range_ka;
    ze = shandong_data.ze;
    
    % 转换高度为米
    if max(range_ka) < 100
        range_ka = range_ka * 1000;
        fprintf('   高度已转换为米\n');
    end
    
    fprintf('   Ka高度层数: %d\n', length(range_ka));
    fprintf('   Ka高度范围: %.0f - %.0f m\n', min(range_ka), max(range_ka));
    fprintf('   Ka高度分辨率: %.2f m\n', mean(diff(range_ka)));
    
    % 获取Ka和W数据
    ka_data = ze.ka;
    w_data = ze.w;
    
    fprintf('   Ka数据维度: %d x %d\n', size(ka_data));
    fprintf('   W数据维度: %d x %d\n', size(w_data));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 3. 确定7点时间范围
fprintf('\n3. 确定7点时间范围\n');

% 定义7点时间范围
start_time_7am = datenum('2025-01-08 07:00:00', 'yyyy-mm-dd HH:MM:SS');
end_time_7am = datenum('2025-01-08 07:59:59', 'yyyy-mm-dd HH:MM:SS');

fprintf('   目标时间范围: %s 到 %s\n', datestr(start_time_7am), datestr(end_time_7am));

% 找到MRR在7点时间范围内的数据
mrr_7am_mask = mrr_time >= start_time_7am & mrr_time <= end_time_7am;
mrr_time_7am = mrr_time(mrr_7am_mask);
mrr_Za_7am = mrr_Za(:, mrr_7am_mask);
mrr_Z_7am = mrr_Z(:, mrr_7am_mask);

fprintf('   MRR 7点时间段数据点: %d\n', length(mrr_time_7am));
if length(mrr_time_7am) > 0
    fprintf('   MRR实际时间范围: %s 到 %s\n', datestr(min(mrr_time_7am)), datestr(max(mrr_time_7am)));
end

% 找到Ka/W在7点时间范围内的数据
ka_7am_mask = obs_time >= start_time_7am & obs_time <= end_time_7am;
ka_time_7am = obs_time(ka_7am_mask);
ka_data_7am = ka_data(:, ka_7am_mask);
w_data_7am = w_data(:, ka_7am_mask);

fprintf('   Ka/W 7点时间段数据点: %d\n', length(ka_time_7am));
if length(ka_time_7am) > 0
    fprintf('   Ka/W实际时间范围: %s 到 %s\n', datestr(min(ka_time_7am)), datestr(max(ka_time_7am)));
end

%% 4. 导出MRR Za数据为CSV
fprintf('\n4. 导出MRR Za数据\n');

if ~isempty(mrr_time_7am)
    % 创建时间标签（第一行）
    time_labels_mrr = cell(1, length(mrr_time_7am) + 1);
    time_labels_mrr{1} = 'Height_m';  % 第一列标题
    for i = 1:length(mrr_time_7am)
        time_labels_mrr{i+1} = datestr(mrr_time_7am(i), 'yyyy-mm-dd HH:MM:SS');
    end
    
    % 创建数据矩阵
    data_matrix_mrr_za = [mrr_range, mrr_Za_7am];
    
    % 写入CSV文件
    filename_mrr_za = 'MRR_Za_7AM_20250108.csv';
    fid = fopen(filename_mrr_za, 'w');
    
    % 写入标题行
    fprintf(fid, '%s', time_labels_mrr{1});
    for i = 2:length(time_labels_mrr)
        fprintf(fid, ',%s', time_labels_mrr{i});
    end
    fprintf(fid, '\n');
    
    % 写入数据
    for i = 1:size(data_matrix_mrr_za, 1)
        fprintf(fid, '%.1f', data_matrix_mrr_za(i, 1));  % 高度
        for j = 2:size(data_matrix_mrr_za, 2)
            if isnan(data_matrix_mrr_za(i, j))
                fprintf(fid, ',NaN');
            else
                fprintf(fid, ',%.3f', data_matrix_mrr_za(i, j));  % 反射率
            end
        end
        fprintf(fid, '\n');
    end
    
    fclose(fid);
    fprintf('   MRR Za数据已保存: %s\n', filename_mrr_za);
    fprintf('   数据维度: %d高度层 x %d时间点\n', size(mrr_Za_7am, 1), size(mrr_Za_7am, 2));
else
    fprintf('   MRR 7点时间段无数据\n');
end

%% 5. 导出MRR Z数据为CSV
fprintf('\n5. 导出MRR Z数据\n');

if ~isempty(mrr_time_7am)
    % 创建数据矩阵
    data_matrix_mrr_z = [mrr_range, mrr_Z_7am];
    
    % 写入CSV文件
    filename_mrr_z = 'MRR_Z_7AM_20250108.csv';
    fid = fopen(filename_mrr_z, 'w');
    
    % 写入标题行
    fprintf(fid, '%s', time_labels_mrr{1});
    for i = 2:length(time_labels_mrr)
        fprintf(fid, ',%s', time_labels_mrr{i});
    end
    fprintf(fid, '\n');
    
    % 写入数据
    for i = 1:size(data_matrix_mrr_z, 1)
        fprintf(fid, '%.1f', data_matrix_mrr_z(i, 1));  % 高度
        for j = 2:size(data_matrix_mrr_z, 2)
            if isnan(data_matrix_mrr_z(i, j))
                fprintf(fid, ',NaN');
            else
                fprintf(fid, ',%.3f', data_matrix_mrr_z(i, j));  % 反射率
            end
        end
        fprintf(fid, '\n');
    end
    
    fclose(fid);
    fprintf('   MRR Z数据已保存: %s\n', filename_mrr_z);
    fprintf('   数据维度: %d高度层 x %d时间点\n', size(mrr_Z_7am, 1), size(mrr_Z_7am, 2));
end

%% 6. 导出Ka波段数据为CSV
fprintf('\n6. 导出Ka波段数据\n');

if ~isempty(ka_time_7am)
    % 创建时间标签（第一行）
    time_labels_ka = cell(1, length(ka_time_7am) + 1);
    time_labels_ka{1} = 'Height_m';  % 第一列标题
    for i = 1:length(ka_time_7am)
        time_labels_ka{i+1} = datestr(ka_time_7am(i), 'yyyy-mm-dd HH:MM:SS');
    end
    
    % 检查维度并创建数据矩阵
    fprintf('   调试信息: range_ka维度 %s, ka_data_7am维度 %s\n', mat2str(size(range_ka)), mat2str(size(ka_data_7am)));

    % 确保range_ka是列向量，并且长度匹配
    if size(range_ka, 1) == 1
        range_ka = range_ka';  % 转置为列向量
    end

    % 只使用与ka_data_7am行数相同的高度层
    if length(range_ka) > size(ka_data_7am, 1)
        range_ka_matched = range_ka(1:size(ka_data_7am, 1));
    else
        range_ka_matched = range_ka;
    end

    data_matrix_ka = [range_ka_matched, ka_data_7am];
    
    % 写入CSV文件
    filename_ka = 'Ka_band_7AM_20250108.csv';
    fid = fopen(filename_ka, 'w');
    
    % 写入标题行
    fprintf(fid, '%s', time_labels_ka{1});
    for i = 2:length(time_labels_ka)
        fprintf(fid, ',%s', time_labels_ka{i});
    end
    fprintf(fid, '\n');
    
    % 写入数据
    for i = 1:size(data_matrix_ka, 1)
        fprintf(fid, '%.1f', data_matrix_ka(i, 1));  % 高度
        for j = 2:size(data_matrix_ka, 2)
            if isnan(data_matrix_ka(i, j))
                fprintf(fid, ',NaN');
            else
                fprintf(fid, ',%.3f', data_matrix_ka(i, j));  % 反射率
            end
        end
        fprintf(fid, '\n');
    end
    
    fclose(fid);
    fprintf('   Ka波段数据已保存: %s\n', filename_ka);
    fprintf('   数据维度: %d高度层 x %d时间点\n', size(ka_data_7am, 1), size(ka_data_7am, 2));
else
    fprintf('   Ka波段 7点时间段无数据\n');
end

%% 7. 导出W波段数据为CSV
fprintf('\n7. 导出W波段数据\n');

if ~isempty(ka_time_7am)
    % 创建数据矩阵（使用相同的时间标签和高度匹配）
    fprintf('   调试信息: w_data_7am维度 %s\n', mat2str(size(w_data_7am)));

    % 使用与w_data_7am行数相同的高度层
    if length(range_ka_matched) > size(w_data_7am, 1)
        range_ka_w = range_ka_matched(1:size(w_data_7am, 1));
    else
        range_ka_w = range_ka_matched;
    end

    data_matrix_w = [range_ka_w, w_data_7am];
    
    % 写入CSV文件
    filename_w = 'W_band_7AM_20250108.csv';
    fid = fopen(filename_w, 'w');
    
    % 写入标题行
    fprintf(fid, '%s', time_labels_ka{1});
    for i = 2:length(time_labels_ka)
        fprintf(fid, ',%s', time_labels_ka{i});
    end
    fprintf(fid, '\n');
    
    % 写入数据
    for i = 1:size(data_matrix_w, 1)
        fprintf(fid, '%.1f', data_matrix_w(i, 1));  % 高度
        for j = 2:size(data_matrix_w, 2)
            if isnan(data_matrix_w(i, j))
                fprintf(fid, ',NaN');
            else
                fprintf(fid, ',%.3f', data_matrix_w(i, j));  % 反射率
            end
        end
        fprintf(fid, '\n');
    end
    
    fclose(fid);
    fprintf('   W波段数据已保存: %s\n', filename_w);
    fprintf('   数据维度: %d高度层 x %d时间点\n', size(w_data_7am, 1), size(w_data_7am, 2));
end

%% 8. 创建数据概览
fprintf('\n=== 7点时刻数据导出总结 ===\n');

if exist('filename_mrr_za', 'var')
    fprintf('MRR Za数据: %s\n', filename_mrr_za);
    fprintf('  - 高度范围: %.0f - %.0f m\n', min(mrr_range), max(mrr_range));
    fprintf('  - 高度分辨率: %.0f m\n', mean(diff(mrr_range)));
    fprintf('  - 时间点数: %d\n', length(mrr_time_7am));
end

if exist('filename_mrr_z', 'var')
    fprintf('MRR Z数据: %s\n', filename_mrr_z);
end

if exist('filename_ka', 'var')
    fprintf('Ka波段数据: %s\n', filename_ka);
    fprintf('  - 高度范围: %.0f - %.0f m\n', min(range_ka), max(range_ka));
    fprintf('  - 高度分辨率: %.2f m\n', mean(diff(range_ka)));
    fprintf('  - 时间点数: %d\n', length(ka_time_7am));
end

if exist('filename_w', 'var')
    fprintf('W波段数据: %s\n', filename_w);
    fprintf('  - 高度范围: %.0f - %.0f m\n', min(range_ka), max(range_ka));
    fprintf('  - 高度分辨率: %.2f m\n', mean(diff(range_ka)));
    fprintf('  - 时间点数: %d\n', length(ka_time_7am));
end

fprintf('\nCSV文件格式说明:\n');
fprintf('- 第一行: 时间标签 (Height_m, 时间1, 时间2, ...)\n');
fprintf('- 第一列: 高度值 (米)\n');
fprintf('- 数据区域: 反射率值 (dBZ)\n');
fprintf('- 缺失值: NaN\n');

fprintf('\n=== CSV导出完成 ===\n');
