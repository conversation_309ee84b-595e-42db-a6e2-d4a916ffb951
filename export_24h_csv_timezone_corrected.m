clear
close all
clc

%% 时区修正后的24小时雷达数据CSV导出
% MRR: UTC时间 (保持不变)
% 云雷达: BJT时间 → 转换为UTC时间 (减8小时)
% 统一使用UTC时间，每小时一个文件夹

fprintf('=== 时区修正后的24小时CSV导出 ===\n');
fprintf('时区处理: 云雷达BJT → UTC (减8小时)\n');
fprintf('统一时间标准: UTC\n\n');

%% 设置参数
data_dir = '20250108';
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
output_base_dir = 'CSV_Export_24h_UTC';

% 高度过滤
mrr_max_height = 2500;
ka_w_max_height = 2200;

%% 1. 加载Shandong数据并修正时区
fprintf('1. 加载云雷达数据并修正时区\n');
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;
    obs_time_utc = obs_time_bjt - 8/24;  % BJT转UTC (减8小时)
    
    range_ka = shandong_data.range_ka;
    if max(range_ka) < 100
        range_ka = range_ka * 1000;
    end
    
    % 高度过滤
    ka_w_height_idx = range_ka <= ka_w_max_height;
    range_ka_filtered = range_ka(ka_w_height_idx);
    
    ka_data_full = shandong_data.ze.ka(ka_w_height_idx, :);
    w_data_full = shandong_data.ze.w(ka_w_height_idx, :);
    
    fprintf('   时区转换完成\n');
    fprintf('   原始BJT: %s - %s\n', datestr(min(obs_time_bjt)), datestr(max(obs_time_bjt)));
    fprintf('   转换UTC: %s - %s\n', datestr(min(obs_time_utc)), datestr(max(obs_time_utc)));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

% 创建主输出目录
if ~exist(output_base_dir, 'dir')
    mkdir(output_base_dir);
end

%% 2. 按UTC小时导出数据
fprintf('\n2. 按UTC小时导出数据\n');

for utc_hour = 0:23
    hour_str = sprintf('%02d', utc_hour);
    fprintf('\n处理UTC第%s小时...\n', hour_str);
    
    % 创建小时文件夹
    hour_dir = fullfile(output_base_dir, sprintf('UTC_Hour_%s', hour_str));
    if ~exist(hour_dir, 'dir')
        mkdir(hour_dir);
    end
    
    %% 处理MRR数据
    % MRR文件名对应的是本地时间，但数据是UTC
    % 需要找到包含该UTC小时数据的文件
    
    % 尝试当前小时和前一小时的文件
    possible_mrr_files = {
        sprintf('20250108/20250108_%02d0000.nc', utc_hour),
        sprintf('20250108/20250108_%02d0000.nc', mod(utc_hour-1+24, 24))
    };
    
    mrr_data_found = false;
    for f = 1:length(possible_mrr_files)
        mrr_filename = possible_mrr_files{f};
        if exist(mrr_filename, 'file')
            try
                mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
                
                % 检查是否包含目标UTC小时的数据
                target_start = datenum(sprintf('2025-01-08 %02d:00:00', utc_hour), 'yyyy-mm-dd HH:MM:SS');
                target_end = datenum(sprintf('2025-01-08 %02d:59:59', utc_hour), 'yyyy-mm-dd HH:MM:SS');
                
                hour_mask = mrr_time >= target_start & mrr_time <= target_end;
                
                if any(hour_mask)
                    fprintf('  找到MRR数据在文件: %s\n', mrr_filename);
                    
                    % 读取数据
                    mrr_Za = ncread(mrr_filename, 'Za');
                    mrr_Z = ncread(mrr_filename, 'Z');
                    mrr_RR = ncread(mrr_filename, 'RR');
                    mrr_range = ncread(mrr_filename, 'range');
                    
                    % 高度过滤
                    mrr_height_idx = mrr_range <= mrr_max_height;
                    mrr_range_filtered = mrr_range(mrr_height_idx);
                    
                    % 提取该小时的数据
                    mrr_time_hour = mrr_time(hour_mask);
                    mrr_Za_hour = mrr_Za(mrr_height_idx, hour_mask);
                    mrr_Z_hour = mrr_Z(mrr_height_idx, hour_mask);
                    mrr_RR_hour = mrr_RR(mrr_height_idx, hour_mask);
                    
                    % 导出MRR CSV文件
                    export_mrr_csv(hour_dir, hour_str, mrr_time_hour, mrr_range_filtered, mrr_Za_hour, mrr_Z_hour, mrr_RR_hour);
                    
                    fprintf('  MRR数据已导出 (%d时间点)\n', length(mrr_time_hour));
                    mrr_data_found = true;
                    break;
                end
            catch ME
                fprintf('  读取MRR文件错误: %s\n', ME.message);
            end
        end
    end
    
    if ~mrr_data_found
        fprintf('  未找到UTC第%s小时的MRR数据\n', hour_str);
    end
    
    %% 处理Ka和W数据
    try
        % 定义UTC小时范围
        utc_start = datenum(sprintf('2025-01-08 %02d:00:00', utc_hour), 'yyyy-mm-dd HH:MM:SS');
        utc_end = datenum(sprintf('2025-01-08 %02d:59:59', utc_hour), 'yyyy-mm-dd HH:MM:SS');
        
        % 使用UTC时间查找数据
        hour_mask_utc = obs_time_utc >= utc_start & obs_time_utc <= utc_end;
        
        if sum(hour_mask_utc) > 0
            hour_obs_time_utc = obs_time_utc(hour_mask_utc);
            ka_data_hour = ka_data_full(:, hour_mask_utc);
            w_data_hour = w_data_full(:, hour_mask_utc);
            
            % 导出Ka和W CSV文件
            export_radar_csv(hour_dir, sprintf('Ka_band_UTC_%s00.csv', hour_str), ...
                           hour_obs_time_utc, range_ka_filtered, ka_data_hour);
            export_radar_csv(hour_dir, sprintf('W_band_UTC_%s00.csv', hour_str), ...
                           hour_obs_time_utc, range_ka_filtered, w_data_hour);
            
            fprintf('  Ka/W数据已导出 (%d时间点)\n', length(hour_obs_time_utc));
        else
            fprintf('  UTC第%s小时无Ka/W数据\n', hour_str);
        end
        
    catch ME
        fprintf('  处理Ka/W数据错误: %s\n', ME.message);
    end
end

fprintf('\n=== 时区修正后的CSV导出完成 ===\n');
fprintf('输出目录: %s\n', output_base_dir);
fprintf('时间标准: UTC\n');

%% 辅助函数：导出MRR CSV
function export_mrr_csv(hour_dir, hour_str, time_data, height_data, Za_data, Z_data, RR_data)
    % 导出MRR Za
    csv_file = fullfile(hour_dir, sprintf('MRR_Za_UTC_%s00.csv', hour_str));
    write_csv_file(csv_file, time_data, height_data, Za_data);
    
    % 导出MRR Z
    csv_file = fullfile(hour_dir, sprintf('MRR_Z_UTC_%s00.csv', hour_str));
    write_csv_file(csv_file, time_data, height_data, Z_data);
    
    % 导出MRR RR
    csv_file = fullfile(hour_dir, sprintf('MRR_RR_UTC_%s00.csv', hour_str));
    write_csv_file(csv_file, time_data, height_data, RR_data);
end

%% 辅助函数：导出雷达CSV
function export_radar_csv(hour_dir, filename, time_data, height_data, radar_data)
    csv_file = fullfile(hour_dir, filename);
    write_csv_file(csv_file, time_data, height_data, radar_data);
end

%% 辅助函数：写入CSV文件
function write_csv_file(filename, time_data, height_data, data_matrix)
    fid = fopen(filename, 'w');
    
    % 写入第一行 (时间)
    fprintf(fid, 'Height_m_UTC');
    for t = 1:length(time_data)
        fprintf(fid, ',%s', datestr(time_data(t), 'yyyy-mm-dd HH:MM:SS'));
    end
    fprintf(fid, '\n');
    
    % 写入数据行
    for h = 1:length(height_data)
        fprintf(fid, '%.1f', height_data(h));
        for t = 1:length(time_data)
            if isnan(data_matrix(h, t))
                fprintf(fid, ',NaN');
            else
                fprintf(fid, ',%.3f', data_matrix(h, t));
            end
        end
        fprintf(fid, '\n');
    end
    
    fclose(fid);
end
