clear
close all
clc

%% Simple batch plotting script - exactly like original but for all 24 hours
% Based on the original MRR_data_load.m script

% Set data directory
data_dir = '20250108';

% Create output directory
output_dir = 'plots_simple_20250108';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% Get all nc files
nc_files = dir(fullfile(data_dir, '*.nc'));
fprintf('Processing %d files...\n', length(nc_files));

%% Process each file
for file_idx = 1:length(nc_files)
    % Update filename path
    filename = fullfile(data_dir, nc_files(file_idx).name);
    
    fprintf('Processing %d/%d: %s\n', file_idx, length(nc_files), nc_files(file_idx).name);
    
    try
        % Read nc file data (exactly as in original)
        infonc = ncinfo(filename);
        lat = ncread(filename, 'latitude');
        lon = ncread(filename, 'longitude');
        Za = ncread(filename, 'Za');
        Z = ncread(filename, 'Z');
        RR = ncread(filename, 'RR');
        range = ncread(filename, 'range');
        time = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        % Extract hour from filename for title
        [~, base_name, ~] = fileparts(nc_files(file_idx).name);
        hour_str = base_name(end-5:end-4);
        
        % Create plot (exactly as in original)
        figure('Position', [100, 100, 800, 600]);
        pcolor(time, range/1000, Za)
        shading flat
        clb = colorbar;
        ylim([0 4])
        caxis([-20 20])
        datetick('x', 'HH:MM')
        ylabel('Height [km]')
        xlabel('Time [UTC]')
        ylabel(clb, 'Reflectivity factor [dBZ]')
        title(sprintf('MRR Data - January 8, 2025, %s:00 UTC', hour_str))
        set(gca, 'FontSize', 15)
        
        % Save figure
        output_filename = fullfile(output_dir, sprintf('MRR_20250108_%s00', hour_str));
        print(output_filename, '-dpng', '-r300')
        
        % Close figure to save memory
        close(gcf);
        
        fprintf('  Saved: %s.png\n', output_filename);
        
    catch ME
        fprintf('  Error processing %s: %s\n', nc_files(file_idx).name, ME.message);
    end
end

fprintf('\nBatch processing complete!\n');
fprintf('Generated plots saved in: %s/\n', output_dir);
