clear
close all
clc

%% 基于精确时间匹配的高度校正分析
% Ka/W: ~18个时间点/秒的高分辨率
% MRR: ~30秒间隔的低分辨率
% 使用就近时间点匹配进行校正

fprintf('=== 基于精确时间匹配的高度校正分析 ===\n');
fprintf('Ka/W时间分辨率: ~18点/秒\n');
fprintf('MRR时间分辨率: ~30秒间隔\n');
fprintf('匹配方法: 就近时间点匹配\n\n');

%% 1. 加载云雷达数据并分析时间分辨率
fprintf('1. 加载云雷达数据并分析时间分辨率\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;
    obs_time_utc = obs_time_bjt - 8/24;  % BJT转UTC
    
    % 分析时间分辨率
    time_diff = diff(obs_time_utc) * 24 * 3600;  % 转换为秒
    mean_time_interval = mean(time_diff);
    time_resolution = 1 / mean_time_interval;  % 每秒的点数
    
    fprintf('   云雷达时间分辨率分析:\n');
    fprintf('     平均时间间隔: %.3f 秒\n', mean_time_interval);
    fprintf('     每秒数据点数: %.1f 点/秒\n', time_resolution);
    fprintf('     总时间点数: %d\n', length(obs_time_utc));
    
    range_ka_original = shandong_data.range_ka;
    if max(range_ka_original) < 100
        range_ka_original = range_ka_original * 1000;
    end
    
    ka_ze = shandong_data.ze.ka;
    w_ze = shandong_data.ze.w;
    ka_vel = shandong_data.vel.ka;
    w_vel = shandong_data.vel.w;
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载MRR数据并分析时间分辨率
fprintf('\n2. 加载MRR数据并分析时间分辨率\n');

% 收集重合时间段的MRR数据
radar_start_utc = min(obs_time_utc);
radar_end_utc = max(obs_time_utc);

all_mrr_time = [];
all_mrr_Za = [];
all_mrr_VEL = [];
all_mrr_range = [];

mrr_files = dir('20250108/*.nc');
for i = 1:length(mrr_files)
    mrr_filename = fullfile('20250108', mrr_files(i).name);
    try
        mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        if max(mrr_time) >= radar_start_utc && min(mrr_time) <= radar_end_utc
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_VEL = ncread(mrr_filename, 'VEL');
            mrr_range = ncread(mrr_filename, 'range');
            
            overlap_mask = mrr_time >= radar_start_utc & mrr_time <= radar_end_utc;
            
            if any(overlap_mask)
                if isempty(all_mrr_time)
                    all_mrr_time = mrr_time(overlap_mask);
                    all_mrr_Za = mrr_Za(:, overlap_mask);
                    all_mrr_VEL = mrr_VEL(:, overlap_mask);
                    all_mrr_range = mrr_range;
                else
                    all_mrr_time = [all_mrr_time; mrr_time(overlap_mask)];
                    all_mrr_Za = [all_mrr_Za, mrr_Za(:, overlap_mask)];
                    all_mrr_VEL = [all_mrr_VEL, mrr_VEL(:, overlap_mask)];
                end
            end
        end
    catch
        continue;
    end
end

% 分析MRR时间分辨率
mrr_time_diff = diff(all_mrr_time) * 24 * 3600;  % 秒
mrr_mean_interval = mean(mrr_time_diff);

fprintf('   MRR时间分辨率分析:\n');
fprintf('     平均时间间隔: %.1f 秒\n', mrr_mean_interval);
fprintf('     每分钟数据点数: %.1f 点/分钟\n', 60/mrr_mean_interval);
fprintf('     总时间点数: %d\n', length(all_mrr_time));

%% 3. 精确时间匹配（基于0.5秒分辨率进行验证）
fprintf('\n3. 进行精确时间匹配（基于0.5秒分辨率进行验证）\n');

% 为每个MRR时间点找到最近的云雷达时间点
matched_pairs = [];
time_differences = [];

fprintf('   匹配MRR时间点到云雷达数据...\n');

% 根据MRR的分辨率进行校正，即0.5秒一个点进行验证
% 创建0.5秒间隔的时间点用于验证
verification_time_points = radar_start_utc:0.5/24/3600:radar_end_utc;

% 对于每个验证时间点，找到最近的MRR和云雷达数据
verification_pairs = [];
verification_time_differences = [];

for i = 1:length(verification_time_points)
    verification_time = verification_time_points(i);
    
    % 找到最近的MRR时间点
    [min_diff_mrr, mrr_idx] = min(abs(all_mrr_time - verification_time));
    mrr_time_diff_seconds = min_diff_mrr * 24 * 3600;
    
    % 找到最近的云雷达时间点
    [min_diff_radar, radar_idx] = min(abs(obs_time_utc - verification_time));
    radar_time_diff_seconds = min_diff_radar * 24 * 3600;
    
    % 只接受时间差合理的匹配（MRR时间差小于30秒，雷达时间差小于0.5秒）
    if mrr_time_diff_seconds <= 30 && radar_time_diff_seconds <= 0.5
        verification_pairs(end+1, :) = [mrr_idx, radar_idx];
        verification_time_differences(end+1, :) = [mrr_time_diff_seconds, radar_time_diff_seconds];
    end
end

fprintf('   验证时间点总数: %d\n', length(verification_time_points));
fprintf('   成功匹配的验证点数: %d\n', size(verification_pairs, 1));
fprintf('   匹配率: %.1f%%\n', size(verification_pairs, 1)/length(verification_time_points)*100);
fprintf('   平均MRR时间差: %.2f 秒\n', mean(verification_time_differences(:,1)));
fprintf('   平均雷达时间差: %.2f 秒\n', mean(verification_time_differences(:,2)));

%% 4. 基于匹配时间点的高度校正
fprintf('\n4. 基于匹配时间点的高度校正（0.5秒分辨率验证）\n');

% 高度限制
height_limit = 2000;
mrr_2km_mask = all_mrr_range <= height_limit;
radar_2km_mask = range_ka_original <= height_limit;

% 校正参数
height_offsets = -300:5:200;  % 5m步长
common_heights = 400:15:2000;  % 15m分辨率通用网格

% 存储每个匹配时刻的校正结果
n_matches = size(verification_pairs, 1);
ka_best_offsets_za = zeros(n_matches, 1);
ka_best_offsets_vel = zeros(n_matches, 1);
w_best_offsets_za = zeros(n_matches, 1);
w_best_offsets_vel = zeros(n_matches, 1);

ka_best_corr_za = zeros(n_matches, 1);
ka_best_corr_vel = zeros(n_matches, 1);
w_best_corr_za = zeros(n_matches, 1);
w_best_corr_vel = zeros(n_matches, 1);

fprintf('   分析%d个匹配时间点...\n', n_matches);

% 采样分析（避免计算量过大）
sample_step = max(1, floor(n_matches / 100));  % 最多分析100个时间点
sample_indices = 1:sample_step:n_matches;

for s = 1:length(sample_indices)
    match_idx = sample_indices(s);
    mrr_idx = verification_pairs(match_idx, 1);
    radar_idx = verification_pairs(match_idx, 2);
    
    if mod(s, 20) == 1
        fprintf('     进度: %d/%d (%.1f%%)\n', s, length(sample_indices), s/length(sample_indices)*100);
    end
    
    % 提取垂直剖面
    mrr_za_profile = all_mrr_Za(mrr_2km_mask, mrr_idx);
    mrr_vel_profile = all_mrr_VEL(mrr_2km_mask, mrr_idx);
    mrr_heights = all_mrr_range(mrr_2km_mask);
    
    ka_ze_profile = ka_ze(radar_2km_mask, radar_idx);
    w_ze_profile = w_ze(radar_2km_mask, radar_idx);
    ka_vel_profile = ka_vel(radar_2km_mask, radar_idx);
    w_vel_profile = w_vel(radar_2km_mask, radar_idx);
    radar_heights = range_ka_original(radar_2km_mask);
    
    % 检查数据质量
    mrr_valid_za = sum(~isnan(mrr_za_profile) & mrr_za_profile > -25);
    ka_valid_za = sum(~isnan(ka_ze_profile) & ka_ze_profile > -25);
    w_valid_za = sum(~isnan(w_ze_profile) & w_ze_profile > -25);
    
    % 只有足够有效数据才进行分析
    if mrr_valid_za > 15 && ka_valid_za > 15 && w_valid_za > 15
        
        % 插值MRR到通用网格
        mrr_za_interp = interp1(mrr_heights, mrr_za_profile, common_heights, 'linear', NaN);
        mrr_vel_interp = interp1(mrr_heights, mrr_vel_profile, common_heights, 'linear', NaN);
        
        % 寻找最佳偏移
        best_corr_ka_za = -1;
        best_corr_ka_vel = -1;
        best_corr_w_za = -1;
        best_corr_w_vel = -1;
        
        for offset = height_offsets
            % 应用偏移
            adjusted_radar_heights = radar_heights + offset;
            
            % 插值云雷达数据
            ka_za_interp = interp1(adjusted_radar_heights, ka_ze_profile, common_heights, 'linear', NaN);
            w_za_interp = interp1(adjusted_radar_heights, w_ze_profile, common_heights, 'linear', NaN);
            ka_vel_interp = interp1(adjusted_radar_heights, ka_vel_profile, common_heights, 'linear', NaN);
            w_vel_interp = interp1(adjusted_radar_heights, w_vel_profile, common_heights, 'linear', NaN);
            
            % 计算相关系数
            % Ka反射率
            valid_ka_za = ~isnan(mrr_za_interp) & ~isnan(ka_za_interp);
            if sum(valid_ka_za) > 10
                corr_temp = corrcoef(mrr_za_interp(valid_ka_za), ka_za_interp(valid_ka_za));
                if corr_temp(1,2) > best_corr_ka_za
                    best_corr_ka_za = corr_temp(1,2);
                    ka_best_offsets_za(match_idx) = offset;
                end
            end
            
            % Ka速度
            valid_ka_vel = ~isnan(mrr_vel_interp) & ~isnan(ka_vel_interp);
            if sum(valid_ka_vel) > 10
                corr_temp = corrcoef(mrr_vel_interp(valid_ka_vel), abs(ka_vel_interp(valid_ka_vel)));
                if corr_temp(1,2) > best_corr_ka_vel
                    best_corr_ka_vel = corr_temp(1,2);
                    ka_best_offsets_vel(match_idx) = offset;
                end
            end
            
            % W反射率
            valid_w_za = ~isnan(mrr_za_interp) & ~isnan(w_za_interp);
            if sum(valid_w_za) > 10
                corr_temp = corrcoef(mrr_za_interp(valid_w_za), w_za_interp(valid_w_za));
                if corr_temp(1,2) > best_corr_w_za
                    best_corr_w_za = corr_temp(1,2);
                    w_best_offsets_za(match_idx) = offset;
                end
            end
            
            % W速度
            valid_w_vel = ~isnan(mrr_vel_interp) & ~isnan(w_vel_interp);
            if sum(valid_w_vel) > 10
                corr_temp = corrcoef(mrr_vel_interp(valid_w_vel), abs(w_vel_interp(valid_w_vel)));
                if corr_temp(1,2) > best_corr_w_vel
                    best_corr_w_vel = corr_temp(1,2);
                    w_best_offsets_vel(match_idx) = offset;
                end
            end
        end
        
        % 保存相关系数
        ka_best_corr_za(match_idx) = best_corr_ka_za;
        ka_best_corr_vel(match_idx) = best_corr_ka_vel;
        w_best_corr_za(match_idx) = best_corr_w_za;
        w_best_corr_vel(match_idx) = best_corr_w_vel;
        
    else
        % 数据质量不足
        ka_best_offsets_za(match_idx) = NaN;
        ka_best_offsets_vel(match_idx) = NaN;
        w_best_offsets_za(match_idx) = NaN;
        w_best_offsets_vel(match_idx) = NaN;
    end
end

%% 5. 计算时间匹配的平均校正（基于0.5秒验证）
fprintf('\n5. 计算基于时间匹配的平均校正（0.5秒验证）\n');

% 去除无效值
valid_ka_za = ~isnan(ka_best_offsets_za) & ka_best_offsets_za ~= 0;
valid_ka_vel = ~isnan(ka_best_offsets_vel) & ka_best_offsets_vel ~= 0;
valid_w_za = ~isnan(w_best_offsets_za) & w_best_offsets_za ~= 0;
valid_w_vel = ~isnan(w_best_offsets_vel) & w_best_offsets_vel ~= 0;

% 计算统计量
if sum(valid_ka_za) > 0
    mean_ka_za = mean(ka_best_offsets_za(valid_ka_za));
    std_ka_za = std(ka_best_offsets_za(valid_ka_za));
    median_ka_za = median(ka_best_offsets_za(valid_ka_za));
    mean_corr_ka_za = mean(ka_best_corr_za(valid_ka_za));
else
    mean_ka_za = NaN; std_ka_za = NaN; median_ka_za = NaN; mean_corr_ka_za = NaN;
end

if sum(valid_ka_vel) > 0
    mean_ka_vel = mean(ka_best_offsets_vel(valid_ka_vel));
    std_ka_vel = std(ka_best_offsets_vel(valid_ka_vel));
    median_ka_vel = median(ka_best_offsets_vel(valid_ka_vel));
    mean_corr_ka_vel = mean(ka_best_corr_vel(valid_ka_vel));
else
    mean_ka_vel = NaN; std_ka_vel = NaN; median_ka_vel = NaN; mean_corr_ka_vel = NaN;
end

if sum(valid_w_za) > 0
    mean_w_za = mean(w_best_offsets_za(valid_w_za));
    std_w_za = std(w_best_offsets_za(valid_w_za));
    median_w_za = median(w_best_offsets_za(valid_w_za));
    mean_corr_w_za = mean(w_best_corr_za(valid_w_za));
else
    mean_w_za = NaN; std_w_za = NaN; median_w_za = NaN; mean_corr_w_za = NaN;
end

if sum(valid_w_vel) > 0
    mean_w_vel = mean(w_best_offsets_vel(valid_w_vel));
    std_w_vel = std(w_best_offsets_vel(valid_w_vel));
    median_w_vel = median(w_best_offsets_vel(valid_w_vel));
    mean_corr_w_vel = mean(w_best_corr_vel(valid_w_vel));
else
    mean_w_vel = NaN; std_w_vel = NaN; median_w_vel = NaN; mean_corr_w_vel = NaN;
end

fprintf('Ka波段校正统计 (基于%d个有效匹配):\n', sum(valid_ka_za));
fprintf('  反射率匹配: %.1f ± %.1f m (中位数: %.1f m, 相关系数: %.3f)\n', ...
    mean_ka_za, std_ka_za, median_ka_za, mean_corr_ka_za);
fprintf('  速度匹配: %.1f ± %.1f m (中位数: %.1f m, 相关系数: %.3f)\n', ...
    mean_ka_vel, std_ka_vel, median_ka_vel, mean_corr_ka_vel);

fprintf('\nW波段校正统计 (基于%d个有效匹配):\n', sum(valid_w_za));
fprintf('  反射率匹配: %.1f ± %.1f m (中位数: %.1f m, 相关系数: %.3f)\n', ...
    mean_w_za, std_w_za, median_w_za, mean_corr_w_za);
fprintf('  速度匹配: %.1f ± %.1f m (中位数: %.1f m, 相关系数: %.3f)\n', ...
    mean_w_vel, std_w_vel, median_w_vel, mean_corr_w_vel);

% 最终校正建议（使用中位数，更robust）
final_ka_offset = median_ka_vel;  % 使用速度匹配的中位数（通常更稳定）
final_w_offset = median_w_vel;

fprintf('\n最终校正建议 (基于中位数，0.5秒验证):\n');
fprintf('  Ka波段: %.1f m\n', final_ka_offset);
fprintf('  W波段: %.1f m\n', final_w_offset);

%% 6. 可视化时间匹配和校正结果（0.5秒验证）
fprintf('\n6. 创建时间匹配校正可视化（0.5秒验证）\n');

figure('Position', [100, 100, 1600, 1200]);

% 子图1: 时间匹配质量
subplot(3,3,1)
histogram(verification_time_differences(:,1), 20, 'FaceColor', 'b', 'FaceAlpha', 0.7)
xlabel('MRR时间差 [秒]')
ylabel('频次')
title(sprintf('MRR时间匹配质量 (n=%d)', size(verification_time_differences, 1)))
grid on

% 子图2: 雷达时间匹配质量
subplot(3,3,2)
histogram(verification_time_differences(:,2), 20, 'FaceColor', 'r', 'FaceAlpha', 0.7)
xlabel('雷达时间差 [秒]')
ylabel('频次')
title(sprintf('雷达时间匹配质量 (n=%d)', size(verification_time_differences, 1)))
grid on

% 子图3: Ka波段校正分布
subplot(3,3,3)
if sum(valid_ka_za) > 0
    histogram(ka_best_offsets_za(valid_ka_za), 15, 'FaceColor', 'g', 'FaceAlpha', 0.7)
    hold on
    xline(mean_ka_za, 'r-', 'LineWidth', 2, 'DisplayName', sprintf('均值: %.1fm', mean_ka_za))
    xline(median_ka_za, 'k--', 'LineWidth', 2, 'DisplayName', sprintf('中位数: %.1fm', median_ka_za))
    xlabel('高度校正 [m]')
    ylabel('频次')
    title('Ka反射率校正分布')
    legend
    grid on
end

subplot(3,3,4)
if sum(valid_ka_vel) > 0
    histogram(ka_best_offsets_vel(valid_ka_vel), 15, 'FaceColor', 'g', 'FaceAlpha', 0.7)
    hold on
    xline(mean_ka_vel, 'r-', 'LineWidth', 2, 'DisplayName', sprintf('均值: %.1fm', mean_ka_vel))
    xline(median_ka_vel, 'k--', 'LineWidth', 2, 'DisplayName', sprintf('中位数: %.1fm', median_ka_vel))
    xlabel('高度校正 [m]')
    ylabel('频次')
    title('Ka速度校正分布')
    legend
    grid on
end

% 子图4: W波段校正分布
subplot(3,3,5)
if sum(valid_w_za) > 0
    histogram(w_best_offsets_za(valid_w_za), 15, 'FaceColor', 'm', 'FaceAlpha', 0.7)
    hold on
    xline(mean_w_za, 'r-', 'LineWidth', 2, 'DisplayName', sprintf('均值: %.1fm', mean_w_za))
    xline(median_w_za, 'k--', 'LineWidth', 2, 'DisplayName', sprintf('中位数: %.1fm', median_w_za))
    xlabel('高度校正 [m]')
    ylabel('频次')
    title('W反射率校正分布')
    legend
    grid on
end

subplot(3,3,6)
if sum(valid_w_vel) > 0
    histogram(w_best_offsets_vel(valid_w_vel), 15, 'FaceColor', 'm', 'FaceAlpha', 0.7)
    hold on
    xline(mean_w_vel, 'r-', 'LineWidth', 2, 'DisplayName', sprintf('均值: %.1fm', mean_w_vel))
    xline(median_w_vel, 'k--', 'LineWidth', 2, 'DisplayName', sprintf('中位数: %.1fm', median_w_vel))
    xlabel('高度校正 [m]')
    ylabel('频次')
    title('W速度校正分布')
    legend
    grid on
end

% 子图5: 相关系数分布
subplot(3,3,7)
if sum(valid_ka_za) > 0 && sum(valid_ka_vel) > 0
    histogram(ka_best_corr_za(valid_ka_za), 15, 'FaceColor', 'g', 'FaceAlpha', 0.5, 'DisplayName', 'Ka反射率')
    hold on
    histogram(ka_best_corr_vel(valid_ka_vel), 15, 'FaceColor', 'b', 'FaceAlpha', 0.5, 'DisplayName', 'Ka速度')
    xlabel('相关系数')
    ylabel('频次')
    title('Ka波段相关系数分布')
    legend
    grid on
end

subplot(3,3,8)
if sum(valid_w_za) > 0 && sum(valid_w_vel) > 0
    histogram(w_best_corr_za(valid_w_za), 15, 'FaceColor', 'm', 'FaceAlpha', 0.5, 'DisplayName', 'W反射率')
    hold on
    histogram(w_best_corr_vel(valid_w_vel), 15, 'FaceColor', 'c', 'FaceAlpha', 0.5, 'DisplayName', 'W速度')
    xlabel('相关系数')
    ylabel('频次')
    title('W波段相关系数分布')
    legend
    grid on
end

% 子图6: 校正随时间的变化
subplot(3,3,9)
if sum(valid_ka_za) > 0
    matched_times = all_mrr_time(verification_pairs(sample_indices(valid_ka_za), 1));
    plot(matched_times, ka_best_offsets_za(sample_indices(valid_ka_za)), 'g.', 'MarkerSize', 8, 'DisplayName', 'Ka反射率')
    hold on
    plot(matched_times, ka_best_offsets_vel(sample_indices(valid_ka_vel)), 'b^', 'MarkerSize', 4, 'DisplayName', 'Ka速度')
    yline(median_ka_za, 'r-', 'LineWidth', 2)
    yline(median_ka_vel, 'r--', 'LineWidth', 2)
    datetick('x', 'HH:MM')
    ylabel('校正偏移 [m]')
    xlabel('时间 [UTC]')
    title('Ka波段校正时间变化')
    legend('Location', 'best')
    grid on
end

sgtitle('基于0.5秒时间分辨率验证的高度校正统计分析', 'FontSize', 14, 'FontWeight', 'bold')

% 保存图片
print('height_correction_time_matched_05s', '-dpng', '-r300')
fprintf('   时间匹配校正图已保存: height_correction_time_matched_05s.png\n');

%% 7. 最终校正建议
fprintf('\n=== 基于0.5秒时间分辨率验证的最终校正建议 ===\n');
fprintf('分析方法: 每个0.5秒时间点匹配最近的MRR和雷达时间点\n');
fprintf('验证时间点总数: %d\n', length(verification_time_points));
fprintf('成功匹配点数: %d\n', size(verification_pairs, 1));
fprintf('匹配率: %.1f%%\n', size(verification_pairs, 1)/length(verification_time_points)*100);
fprintf('平均MRR时间差: %.2f 秒\n', mean(verification_time_differences(:,1)));
fprintf('平均雷达时间差: %.2f 秒\n', mean(verification_time_differences(:,2)));

fprintf('\n推荐高度校正 (使用中位数，更robust):\n');
fprintf('  Ka波段高度 = 原始高度 %+.1f m\n', final_ka_offset);
fprintf('  W波段高度 = 原始高度 %+.1f m\n', final_w_offset);

fprintf('\n校正可信度评估:\n');
if ~isnan(std_ka_vel) && std_ka_vel / abs(median_ka_vel) < 0.5
    fprintf('  Ka波段: 高可信度 (变异系数 < 50%%)\n');
else
    fprintf('  Ka波段: 中等可信度\n');
end

if ~isnan(std_w_vel) && std_w_vel / abs(median_w_vel) < 0.5
    fprintf('  W波段: 高可信度 (变异系数 < 50%%)\n');
else
    fprintf('  W波段: 中等可信度\n');
end

fprintf('\n=== 0.5秒时间分辨率验证校正分析完成 ===\n');