clear
close all
clc

%% 导出24小时MRR、Ka、W波段数据为CSV格式
% 每小时一个文件夹，包含三种雷达数据的CSV文件
% 格式: 第一行时间，第一列高度，中间数值为反射率

fprintf('=== 导出24小时雷达数据为CSV格式 ===\n');

%% 设置参数
data_dir = '20250108';
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
output_base_dir = 'CSV_Export_24h';

% 高度过滤设置
mrr_max_height = 2500;  % MRR最大高度
ka_w_max_height = 2200; % Ka和W最大高度

fprintf('高度过滤: MRR ≤ %dm, Ka/W ≤ %dm\n', mrr_max_height, ka_w_max_height);

%% 1. 加载Shandong数据（一次性加载）
fprintf('\n1. 加载Shandong Ka/W波段数据...\n');
try
    shandong_data = load(shandong_filename);
    obs_time = shandong_data.obs_time;
    range_ka = shandong_data.range_ka;
    
    % 转换高度为米
    if max(range_ka) < 100
        range_ka = range_ka * 1000;
    end
    
    % 应用高度过滤
    ka_w_height_idx = range_ka <= ka_w_max_height;
    range_ka_filtered = range_ka(ka_w_height_idx);
    
    % 获取Ka和W数据
    ka_data_full = shandong_data.ze.ka(ka_w_height_idx, :);
    w_data_full = shandong_data.ze.w(ka_w_height_idx, :);
    
    fprintf('   Ka/W数据加载完成\n');
    fprintf('   高度层数: %d (%.0f - %.0f m)\n', length(range_ka_filtered), min(range_ka_filtered), max(range_ka_filtered));
    fprintf('   时间点数: %d\n', length(obs_time));
    
catch ME
    fprintf('   错误加载Shandong数据: %s\n', ME.message);
    return
end

%% 2. 处理24小时MRR数据
fprintf('\n2. 处理24小时MRR数据...\n');

% 获取所有MRR文件
mrr_files = dir(fullfile(data_dir, '*.nc'));
fprintf('   找到%d个MRR文件\n', length(mrr_files));

% 创建主输出目录
if ~exist(output_base_dir, 'dir')
    mkdir(output_base_dir);
end

%% 3. 逐小时处理和导出
for hour = 0:23
    hour_str = sprintf('%02d', hour);
    fprintf('\n处理第%s小时...\n', hour_str);
    
    % 创建小时文件夹
    hour_dir = fullfile(output_base_dir, sprintf('Hour_%s', hour_str));
    if ~exist(hour_dir, 'dir')
        mkdir(hour_dir);
    end
    
    % 查找对应的MRR文件
    mrr_filename = fullfile(data_dir, sprintf('20250108_%s0000.nc', hour_str));
    
    if exist(mrr_filename, 'file')
        try
            %% 处理MRR数据
            fprintf('  处理MRR文件: %s\n', mrr_filename);
            
            % 读取MRR数据
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_Z = ncread(mrr_filename, 'Z');
            mrr_RR = ncread(mrr_filename, 'RR');
            mrr_range = ncread(mrr_filename, 'range');
            mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
            
            % 应用高度过滤
            mrr_height_idx = mrr_range <= mrr_max_height;
            mrr_range_filtered = mrr_range(mrr_height_idx);
            mrr_Za_filtered = mrr_Za(mrr_height_idx, :);
            mrr_Z_filtered = mrr_Z(mrr_height_idx, :);
            mrr_RR_filtered = mrr_RR(mrr_height_idx, :);
            
            % 创建时间标签
            time_labels = cell(1, length(mrr_time));
            for t = 1:length(mrr_time)
                time_labels{t} = datestr(mrr_time(t), 'yyyy-mm-dd HH:MM:SS');
            end
            
            %% 导出MRR Za数据
            csv_filename = fullfile(hour_dir, sprintf('MRR_Za_%s00.csv', hour_str));
            
            % 创建数据矩阵：第一行是时间，第一列是高度
            data_matrix = [NaN, mrr_time'; mrr_range_filtered, mrr_Za_filtered];
            
            % 写入CSV文件
            fid = fopen(csv_filename, 'w');
            
            % 写入第一行（时间）
            fprintf(fid, 'Height_m');
            for t = 1:length(mrr_time)
                fprintf(fid, ',%s', time_labels{t});
            end
            fprintf(fid, '\n');
            
            % 写入数据行
            for h = 1:length(mrr_range_filtered)
                fprintf(fid, '%.1f', mrr_range_filtered(h));
                for t = 1:length(mrr_time)
                    if isnan(mrr_Za_filtered(h, t))
                        fprintf(fid, ',NaN');
                    else
                        fprintf(fid, ',%.2f', mrr_Za_filtered(h, t));
                    end
                end
                fprintf(fid, '\n');
            end
            fclose(fid);
            
            %% 导出MRR Z数据
            csv_filename = fullfile(hour_dir, sprintf('MRR_Z_%s00.csv', hour_str));
            fid = fopen(csv_filename, 'w');
            
            fprintf(fid, 'Height_m');
            for t = 1:length(mrr_time)
                fprintf(fid, ',%s', time_labels{t});
            end
            fprintf(fid, '\n');
            
            for h = 1:length(mrr_range_filtered)
                fprintf(fid, '%.1f', mrr_range_filtered(h));
                for t = 1:length(mrr_time)
                    if isnan(mrr_Z_filtered(h, t))
                        fprintf(fid, ',NaN');
                    else
                        fprintf(fid, ',%.2f', mrr_Z_filtered(h, t));
                    end
                end
                fprintf(fid, '\n');
            end
            fclose(fid);
            
            %% 导出MRR RR数据
            csv_filename = fullfile(hour_dir, sprintf('MRR_RR_%s00.csv', hour_str));
            fid = fopen(csv_filename, 'w');
            
            fprintf(fid, 'Height_m');
            for t = 1:length(mrr_time)
                fprintf(fid, ',%s', time_labels{t});
            end
            fprintf(fid, '\n');
            
            for h = 1:length(mrr_range_filtered)
                fprintf(fid, '%.1f', mrr_range_filtered(h));
                for t = 1:length(mrr_time)
                    if isnan(mrr_RR_filtered(h, t))
                        fprintf(fid, ',NaN');
                    else
                        fprintf(fid, ',%.4f', mrr_RR_filtered(h, t));
                    end
                end
                fprintf(fid, '\n');
            end
            fclose(fid);
            
            fprintf('  MRR数据已导出: Za, Z, RR\n');
            
        catch ME
            fprintf('  处理MRR数据时出错: %s\n', ME.message);
        end
    else
        fprintf('  MRR文件不存在: %s\n', mrr_filename);
    end
    
    %% 处理Ka和W数据（该小时的数据）
    try
        % 定义该小时的时间范围
        hour_start = datenum(sprintf('2025-01-08 %02d:00:00', hour), 'yyyy-mm-dd HH:MM:SS');
        hour_end = datenum(sprintf('2025-01-08 %02d:59:59', hour), 'yyyy-mm-dd HH:MM:SS');
        
        % 找到该小时的Ka/W数据
        hour_mask = obs_time >= hour_start & obs_time <= hour_end;
        hour_obs_time = obs_time(hour_mask);
        
        if sum(hour_mask) > 0
            ka_data_hour = ka_data_full(:, hour_mask);
            w_data_hour = w_data_full(:, hour_mask);
            
            % 创建时间标签
            time_labels_ka = cell(1, length(hour_obs_time));
            for t = 1:length(hour_obs_time)
                time_labels_ka{t} = datestr(hour_obs_time(t), 'yyyy-mm-dd HH:MM:SS');
            end
            
            %% 导出Ka数据
            csv_filename = fullfile(hour_dir, sprintf('Ka_band_%s00.csv', hour_str));
            fid = fopen(csv_filename, 'w');
            
            fprintf(fid, 'Height_m');
            for t = 1:length(hour_obs_time)
                fprintf(fid, ',%s', time_labels_ka{t});
            end
            fprintf(fid, '\n');
            
            for h = 1:length(range_ka_filtered)
                fprintf(fid, '%.1f', range_ka_filtered(h));
                for t = 1:length(hour_obs_time)
                    if isnan(ka_data_hour(h, t))
                        fprintf(fid, ',NaN');
                    else
                        fprintf(fid, ',%.2f', ka_data_hour(h, t));
                    end
                end
                fprintf(fid, '\n');
            end
            fclose(fid);
            
            %% 导出W数据
            csv_filename = fullfile(hour_dir, sprintf('W_band_%s00.csv', hour_str));
            fid = fopen(csv_filename, 'w');
            
            fprintf(fid, 'Height_m');
            for t = 1:length(hour_obs_time)
                fprintf(fid, ',%s', time_labels_ka{t});
            end
            fprintf(fid, '\n');
            
            for h = 1:length(range_ka_filtered)
                fprintf(fid, '%.1f', range_ka_filtered(h));
                for t = 1:length(hour_obs_time)
                    if isnan(w_data_hour(h, t))
                        fprintf(fid, ',NaN');
                    else
                        fprintf(fid, ',%.2f', w_data_hour(h, t));
                    end
                end
                fprintf(fid, '\n');
            end
            fclose(fid);
            
            fprintf('  Ka/W数据已导出，时间点数: %d\n', length(hour_obs_time));
            
        else
            fprintf('  该小时无Ka/W数据\n');
        end
        
    catch ME
        fprintf('  处理Ka/W数据时出错: %s\n', ME.message);
    end
    
    fprintf('  第%s小时处理完成\n', hour_str);
end

fprintf('\n=== 24小时CSV导出完成 ===\n');
fprintf('输出目录: %s\n', output_base_dir);
fprintf('文件结构:\n');
fprintf('  Hour_XX/\n');
fprintf('    ├── MRR_Za_XX00.csv\n');
fprintf('    ├── MRR_Z_XX00.csv\n');
fprintf('    ├── MRR_RR_XX00.csv\n');
fprintf('    ├── Ka_band_XX00.csv\n');
fprintf('    └── W_band_XX00.csv\n');
fprintf('\nCSV格式说明:\n');
fprintf('  - 第一行: 时间戳\n');
fprintf('  - 第一列: 高度 [m]\n');
fprintf('  - 数据: 反射率 [dBZ] 或 降雨率 [mm/h]\n');
