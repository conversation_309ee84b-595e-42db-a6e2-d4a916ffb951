clear
close all
clc

%% 查找20:00时刻2km以下有数据的最高点
% 限制条件: 所有雷达数据都只考虑2000m以下

fprintf('=== 查找20:00时刻2km以下有数据的最高点 ===\n');
fprintf('高度限制: 所有雷达 ≤ 2000m\n\n');

%% 1. 加载MRR数据
mrr_filename = '20250108/20250108_200000.nc';
fprintf('1. 加载MRR数据: %s\n', mrr_filename);

try
    mrr_Za = ncread(mrr_filename, 'Za');
    mrr_Z = ncread(mrr_filename, 'Z');
    mrr_RR = ncread(mrr_filename, 'RR');
    mrr_range = ncread(mrr_filename, 'range');
    mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    % 应用2000m高度过滤
    mrr_2km_idx = mrr_range <= 2000;
    mrr_range_2km = mrr_range(mrr_2km_idx);
    mrr_Za_2km = mrr_Za(mrr_2km_idx, :);
    mrr_Z_2km = mrr_Z(mrr_2km_idx, :);
    mrr_RR_2km = mrr_RR(mrr_2km_idx, :);
    
    fprintf('   MRR原始高度层数: %d\n', length(mrr_range));
    fprintf('   MRR 2km以下层数: %d\n', length(mrr_range_2km));
    fprintf('   MRR 2km以下高度范围: %.0f - %.0f m\n', min(mrr_range_2km), max(mrr_range_2km));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载Ka和W波段数据
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
fprintf('\n2. 加载Ka和W波段数据: %s\n', shandong_filename);

try
    shandong_data = load(shandong_filename);
    obs_time = shandong_data.obs_time;
    range_ka = shandong_data.range_ka;
    ze = shandong_data.ze;
    
    % 转换高度为米
    if max(range_ka) < 100
        range_ka = range_ka * 1000;
    end
    
    % 应用2000m高度过滤
    ka_2km_idx = range_ka <= 2000;
    range_ka_2km = range_ka(ka_2km_idx);
    
    fprintf('   Ka原始高度层数: %d\n', length(range_ka));
    fprintf('   Ka 2km以下层数: %d\n', length(range_ka_2km));
    fprintf('   Ka 2km以下高度范围: %.0f - %.0f m\n', min(range_ka_2km), max(range_ka_2km));
    fprintf('   Ka高度分辨率: %.2f m\n', mean(diff(range_ka_2km)));
    
    % 获取Ka和W数据
    ka_data = ze.ka;
    w_data = ze.w;
    ka_data_2km = ka_data(ka_2km_idx, :);
    w_data_2km = w_data(ka_2km_idx, :);
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 3. 找到20:00时刻
fprintf('\n3. 查找20:00时刻数据\n');

target_time = datenum('2025-01-08 20:00:00', 'yyyy-mm-dd HH:MM:SS');
fprintf('   目标时间: %s\n', datestr(target_time));

% MRR时间匹配
[mrr_time_diff, mrr_time_idx] = min(abs(mrr_time - target_time));
mrr_actual_time = mrr_time(mrr_time_idx);
fprintf('   MRR时间: %s (差异: %.1f分钟)\n', datestr(mrr_actual_time), mrr_time_diff * 24 * 60);

% Ka/W时间匹配
[ka_time_diff, ka_time_idx] = min(abs(obs_time - target_time));
ka_actual_time = obs_time(ka_time_idx);
fprintf('   Ka/W时间: %s (差异: %.1f分钟)\n', datestr(ka_actual_time), ka_time_diff * 24 * 60);

%% 4. 提取20:00时刻2km以下的数据
fprintf('\n4. 分析20:00时刻2km以下数据\n');

% MRR数据
mrr_Za_2000_2km = mrr_Za_2km(:, mrr_time_idx);
mrr_Z_2000_2km = mrr_Z_2km(:, mrr_time_idx);
mrr_RR_2000_2km = mrr_RR_2km(:, mrr_time_idx);

% Ka和W数据
ka_2000_2km = ka_data_2km(:, ka_time_idx);
w_2000_2km = w_data_2km(:, ka_time_idx);

%% 5. 计算2km以下有数据的最高点
fprintf('\n=== 2km以下有数据的最高点分析 ===\n');

% MRR Za
mrr_Za_valid = ~isnan(mrr_Za_2000_2km) & mrr_Za_2000_2km > -30;
if any(mrr_Za_valid)
    mrr_Za_max_height = max(mrr_range_2km(mrr_Za_valid));
    mrr_Za_valid_count = sum(mrr_Za_valid);
    fprintf('MRR Za:\n');
    fprintf('  2km以下有效层数: %d\n', mrr_Za_valid_count);
    fprintf('  2km以下最高点: %.0f m\n', mrr_Za_max_height);
    fprintf('  该高度的反射率: %.1f dBZ\n', mrr_Za_2000_2km(mrr_range_2km == mrr_Za_max_height));
else
    fprintf('MRR Za: 2km以下无有效数据\n');
    mrr_Za_max_height = NaN;
end

% MRR Z
mrr_Z_valid = ~isnan(mrr_Z_2000_2km) & mrr_Z_2000_2km > -30;
if any(mrr_Z_valid)
    mrr_Z_max_height = max(mrr_range_2km(mrr_Z_valid));
    mrr_Z_valid_count = sum(mrr_Z_valid);
    fprintf('\nMRR Z:\n');
    fprintf('  2km以下有效层数: %d\n', mrr_Z_valid_count);
    fprintf('  2km以下最高点: %.0f m\n', mrr_Z_max_height);
    fprintf('  该高度的反射率: %.1f dBZ\n', mrr_Z_2000_2km(mrr_range_2km == mrr_Z_max_height));
else
    fprintf('MRR Z: 2km以下无有效数据\n');
    mrr_Z_max_height = NaN;
end

% MRR RR
mrr_RR_valid = ~isnan(mrr_RR_2000_2km) & mrr_RR_2000_2km > 0.01;
if any(mrr_RR_valid)
    mrr_RR_max_height = max(mrr_range_2km(mrr_RR_valid));
    mrr_RR_valid_count = sum(mrr_RR_valid);
    fprintf('\nMRR RR:\n');
    fprintf('  2km以下有效层数: %d\n', mrr_RR_valid_count);
    fprintf('  2km以下最高点: %.0f m\n', mrr_RR_max_height);
    fprintf('  该高度的降雨率: %.3f mm/h\n', mrr_RR_2000_2km(mrr_range_2km == mrr_RR_max_height));
else
    fprintf('MRR RR: 2km以下无有效数据\n');
    mrr_RR_max_height = NaN;
end

% Ka波段
ka_valid = ~isnan(ka_2000_2km) & ka_2000_2km > -30;
if any(ka_valid)
    ka_max_height = max(range_ka_2km(ka_valid));
    ka_valid_count = sum(ka_valid);
    fprintf('\nKa波段 (分辨率30.75m):\n');
    fprintf('  2km以下有效层数: %d\n', ka_valid_count);
    fprintf('  2km以下最高点: %.0f m\n', ka_max_height);
    fprintf('  该高度的反射率: %.1f dBZ\n', ka_2000_2km(range_ka_2km == ka_max_height));
else
    fprintf('Ka波段: 2km以下无有效数据\n');
    ka_max_height = NaN;
end

% W波段
w_valid = ~isnan(w_2000_2km) & w_2000_2km > -30;
if any(w_valid)
    w_max_height = max(range_ka_2km(w_valid));
    w_valid_count = sum(w_valid);
    fprintf('\nW波段 (分辨率30.75m):\n');
    fprintf('  2km以下有效层数: %d\n', w_valid_count);
    fprintf('  2km以下最高点: %.0f m\n', w_max_height);
    fprintf('  该高度的反射率: %.1f dBZ\n', w_2000_2km(range_ka_2km == w_max_height));
else
    fprintf('W波段: 2km以下无有效数据\n');
    w_max_height = NaN;
end

%% 6. 总结对比
fprintf('\n=== 20:00时刻2km以下最高点总结 ===\n');

max_heights_2km = [];
radar_names_2km = {};

if ~isnan(mrr_Za_max_height)
    max_heights_2km(end+1) = mrr_Za_max_height;
    radar_names_2km{end+1} = 'MRR Za';
end

if ~isnan(mrr_Z_max_height)
    max_heights_2km(end+1) = mrr_Z_max_height;
    radar_names_2km{end+1} = 'MRR Z';
end

if ~isnan(ka_max_height)
    max_heights_2km(end+1) = ka_max_height;
    radar_names_2km{end+1} = 'Ka波段';
end

if ~isnan(w_max_height)
    max_heights_2km(end+1) = w_max_height;
    radar_names_2km{end+1} = 'W波段';
end

% 排序显示
[sorted_heights_2km, sort_idx] = sort(max_heights_2km, 'descend');
sorted_names_2km = radar_names_2km(sort_idx);

fprintf('2km以下最高点排序:\n');
for i = 1:length(sorted_heights_2km)
    fprintf('  %d. %s: %.0f m\n', i, sorted_names_2km{i}, sorted_heights_2km(i));
end

% 计算与2km的距离
fprintf('\n距离2km顶部的差距:\n');
for i = 1:length(sorted_heights_2km)
    gap = 2000 - sorted_heights_2km(i);
    fprintf('  %s: 距2km还有 %.0f m\n', sorted_names_2km{i}, gap);
end

%% 7. 创建可视化
figure('Position', [100, 100, 1200, 800]);

% 子图1: 垂直剖面对比（只显示2km以下）
subplot(2,2,1)
plot(mrr_Za_2000_2km, mrr_range_2km/1000, 'b-', 'LineWidth', 2, 'DisplayName', 'MRR Za')
hold on
plot(mrr_Z_2000_2km, mrr_range_2km/1000, 'r--', 'LineWidth', 2, 'DisplayName', 'MRR Z')
plot(ka_2000_2km, range_ka_2km/1000, 'g-', 'LineWidth', 2, 'DisplayName', 'Ka')
plot(w_2000_2km, range_ka_2km/1000, 'm-', 'LineWidth', 2, 'DisplayName', 'W')
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('20:00时刻垂直剖面 (≤2km)')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([-40 20])

% 添加最高点标记
if ~isnan(mrr_Za_max_height)
    plot(mrr_Za_2000_2km(mrr_range_2km == mrr_Za_max_height), mrr_Za_max_height/1000, 'bo', 'MarkerSize', 8, 'MarkerFaceColor', 'b')
end
if ~isnan(ka_max_height)
    plot(ka_2000_2km(range_ka_2km == ka_max_height), ka_max_height/1000, 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g')
end
if ~isnan(w_max_height)
    plot(w_2000_2km(range_ka_2km == w_max_height), w_max_height/1000, 'mo', 'MarkerSize', 8, 'MarkerFaceColor', 'm')
end

% 子图2: 最高点柱状图
subplot(2,2,2)
bar(max_heights_2km)
set(gca, 'XTickLabel', radar_names_2km)
ylabel('2km以下最高点 [m]')
title('各雷达2km以下最高点对比')
grid on
xtickangle(45)
ylim([0 2000])

% 添加2km参考线
hold on
plot([0.5 length(max_heights_2km)+0.5], [2000 2000], 'k--', 'LineWidth', 2, 'DisplayName', '2km限制')

% 在柱状图上添加数值标签
for i = 1:length(max_heights_2km)
    text(i, max_heights_2km(i) + 50, sprintf('%.0f m', max_heights_2km(i)), ...
        'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold')
end

% 子图3: 数据可用性对比
subplot(2,2,3)
plot(double(mrr_Za_valid), mrr_range_2km/1000, 'b-', 'LineWidth', 2, 'DisplayName', 'MRR Za')
hold on
plot(double(ka_valid), range_ka_2km/1000, 'g-', 'LineWidth', 2, 'DisplayName', 'Ka')
plot(double(w_valid), range_ka_2km/1000, 'm-', 'LineWidth', 2, 'DisplayName', 'W')
ylabel('高度 [km]')
xlabel('数据可用性 (1=有效, 0=无效)')
title('20:00时刻数据可用性 (≤2km)')
legend('Location', 'best')
grid on
ylim([0 2])

% 子图4: 高度覆盖率对比
subplot(2,2,4)
coverage_heights = [0:100:2000];  % 每100m统计一次
mrr_coverage = zeros(size(coverage_heights));
ka_coverage = zeros(size(coverage_heights));
w_coverage = zeros(size(coverage_heights));

for i = 1:length(coverage_heights)-1
    height_range = coverage_heights(i):coverage_heights(i+1);
    
    % MRR覆盖率
    mrr_in_range = mrr_range_2km >= coverage_heights(i) & mrr_range_2km < coverage_heights(i+1);
    if any(mrr_in_range)
        mrr_data_in_range = mrr_Za_2000_2km(mrr_in_range);
        mrr_coverage(i) = sum(~isnan(mrr_data_in_range) & mrr_data_in_range > -30) / length(mrr_data_in_range) * 100;
    end
    
    % Ka覆盖率
    ka_in_range = range_ka_2km >= coverage_heights(i) & range_ka_2km < coverage_heights(i+1);
    if any(ka_in_range)
        ka_data_in_range = ka_2000_2km(ka_in_range);
        ka_coverage(i) = sum(~isnan(ka_data_in_range) & ka_data_in_range > -30) / length(ka_data_in_range) * 100;
    end
    
    % W覆盖率
    w_in_range = range_ka_2km >= coverage_heights(i) & range_ka_2km < coverage_heights(i+1);
    if any(w_in_range)
        w_data_in_range = w_2000_2km(w_in_range);
        w_coverage(i) = sum(~isnan(w_data_in_range) & w_data_in_range > -30) / length(w_data_in_range) * 100;
    end
end

plot(coverage_heights(1:end-1)/1000, mrr_coverage(1:end-1), 'b-', 'LineWidth', 2, 'DisplayName', 'MRR')
hold on
plot(coverage_heights(1:end-1)/1000, ka_coverage(1:end-1), 'g-', 'LineWidth', 2, 'DisplayName', 'Ka')
plot(coverage_heights(1:end-1)/1000, w_coverage(1:end-1), 'm-', 'LineWidth', 2, 'DisplayName', 'W')
ylabel('数据覆盖率 [%]')
xlabel('高度 [km]')
title('各高度层数据覆盖率')
legend('Location', 'best')
grid on
ylim([0 100])
xlim([0 2])

sgtitle('20:00时刻2km以下雷达数据对比分析', 'FontSize', 14, 'FontWeight', 'bold')

% 保存图片
print('radar_2km_analysis_2000', '-dpng', '-r300')
fprintf('\n图表已保存: radar_2km_analysis_2000.png\n');

fprintf('\n=== 2km以下最高点分析完成 ===\n');
