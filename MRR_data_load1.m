clear
close all
clc

%% MRR Data Visualization Script - January 8, 2025 Data
% This script reads and visualizes MRR (Micro Rain Radar) data

% Set data directory and date
data_dir = '20250108';
date_str = '20250108';

% Get all nc files
nc_files = dir(fullfile(data_dir, '*.nc'));
fprintf('Found %d NetCDF files\n', length(nc_files));

% Initialize data storage
all_time = [];
all_Za = [];
all_Z = [];
all_RR = [];
range_data = [];

%% Read all hourly data
for i = 1:length(nc_files)
    filename = fullfile(data_dir, nc_files(i).name);
    fprintf('Processing: %s\n', nc_files(i).name);

    try
        % Read basic information
        if i == 1
            % Only read location and height info from first file
            lat = ncread(filename, 'latitude');
            lon = ncread(filename, 'longitude');
            range_data = ncread(filename, 'range');
        end

        % Read time series data
        Za_temp = ncread(filename, 'Za');
        Z_temp = ncread(filename, 'Z');
        RR_temp = ncread(filename, 'RR');
        time_temp = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');

        % Merge data
        all_time = [all_time; time_temp];
        if isempty(all_Za)
            all_Za = Za_temp;
            all_Z = Z_temp;
            all_RR = RR_temp;
        else
            all_Za = [all_Za, Za_temp];
            all_Z = [all_Z, Z_temp];
            all_RR = [all_RR, RR_temp];
        end

    catch ME
        fprintf('Error reading file %s: %s\n', nc_files(i).name, ME.message);
    end
end

fprintf('Data loading complete. Time points: %d, Height levels: %d\n', length(all_time), length(range_data));

%% Create comprehensive visualization plots

% Plot 1: Reflectivity factor time-height diagram
figure('Position', [100, 100, 1200, 800]);

subplot(2,2,1)
pcolor(all_time, range_data/1000, all_Za)
shading flat
clb1 = colorbar;
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
ylabel(clb1, 'Reflectivity Factor [dBZ]')
title('Reflectivity Factor Za - January 8, 2025')
set(gca, 'FontSize', 12)

% Plot 2: Equivalent reflectivity factor
subplot(2,2,2)
pcolor(all_time, range_data/1000, all_Z)
shading flat
clb2 = colorbar;
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
ylabel(clb2, 'Equivalent Reflectivity Factor [dBZ]')
title('Equivalent Reflectivity Factor Z - January 8, 2025')
set(gca, 'FontSize', 12)

% 图3: 降雨率
subplot(2,2,3)
pcolor(all_time, range_data/1000, log10(all_RR + 0.01))  % 对数尺度显示降雨率
shading flat
clb3 = colorbar;
ylim([0 4])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
ylabel(clb3, 'log10(降雨率) [mm/h]')
title('降雨率 RR - 2025年1月8日')
set(gca, 'FontSize', 12)

% 图4: 地面降雨率时间序列
subplot(2,2,4)
ground_level_idx = 1;  % 假设第一层是地面层
ground_RR = squeeze(all_RR(ground_level_idx, :));
plot(all_time, ground_RR, 'b-', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('降雨率 [mm/h]')
xlabel('时间 [UTC]')
title('地面降雨率时间序列')
grid on
set(gca, 'FontSize', 12)

sgtitle('MRR数据分析 - 2025年1月8日', 'FontSize', 16, 'FontWeight', 'bold')

%% 保存图片
print('MRR_analysis_20250108', '-dpng', '-r300')
fprintf('图片已保存为: MRR_analysis_20250108.png\n');
