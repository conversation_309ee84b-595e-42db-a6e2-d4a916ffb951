clear
close all
clc

%% 时区修正后的雷达数据对比分析
% MRR: UTC时间
% 云雷达: BJT时间 (UTC+8)
% 统一转换为UTC时间进行对比

fprintf('=== 时区修正后的雷达数据对比 ===\n');
fprintf('MRR: UTC时间\n');
fprintf('云雷达: BJT时间 → 转换为UTC时间 (减8小时)\n\n');

%% 1. 加载并修正云雷达时区
fprintf('1. 加载云雷达数据并修正时区\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;  % BJT时间
    obs_time_utc = obs_time_bjt - 8/24;     % 转换为UTC (减8小时)
    
    range_ka = shandong_data.range_ka;
    if max(range_ka) < 100
        range_ka = range_ka * 1000;  % 转换为米
    end
    
    ka_data = shandong_data.ze.ka;
    w_data = shandong_data.ze.w;
    
    fprintf('   原始BJT时间范围: %s - %s\n', ...
        datestr(min(obs_time_bjt)), datestr(max(obs_time_bjt)));
    fprintf('   转换UTC时间范围: %s - %s\n', ...
        datestr(min(obs_time_utc)), datestr(max(obs_time_utc)));
    
    % 应用高度过滤
    ka_w_height_idx = range_ka <= 2200;
    range_ka_filtered = range_ka(ka_w_height_idx);
    ka_data_filtered = ka_data(ka_w_height_idx, :);
    w_data_filtered = w_data(ka_w_height_idx, :);
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 分析特定时间点 (UTC时间)
fprintf('\n2. 分析特定UTC时间点\n');

% 分析几个关键时间点 (UTC)
target_times_utc = [
    datenum('2025-01-08 00:00:00', 'yyyy-mm-dd HH:MM:SS'),  % UTC 00:00 = BJT 08:00
    datenum('2025-01-08 04:00:00', 'yyyy-mm-dd HH:MM:SS'),  % UTC 04:00 = BJT 12:00
    datenum('2025-01-08 12:00:00', 'yyyy-mm-dd HH:MM:SS'),  % UTC 12:00 = BJT 20:00
    datenum('2025-01-08 20:00:00', 'yyyy-mm-dd HH:MM:SS')   % UTC 20:00 = BJT 04:00(次日)
];

time_labels = {'00:00 UTC', '04:00 UTC', '12:00 UTC', '20:00 UTC'};

for t = 1:length(target_times_utc)
    target_time = target_times_utc(t);
    fprintf('\n--- %s (BJT %s) ---\n', time_labels{t}, ...
        datestr(target_time + 8/24, 'HH:MM'));
    
    %% 查找MRR数据
    % 确定需要哪个MRR文件
    target_hour = hour(datetime(datevec(target_time)));
    
    % MRR文件名是本地时间，但数据是UTC，所以需要找前一个小时的文件
    if target_hour == 0
        mrr_file_hour = 23;  % 前一天的23:00文件
        mrr_filename = '20250107/20250107_230000.nc';  % 如果有前一天数据
    else
        mrr_file_hour = target_hour - 1;
        mrr_filename = sprintf('20250108/20250108_%02d0000.nc', mrr_file_hour);
    end
    
    fprintf('   查找MRR文件: %s\n', mrr_filename);
    
    if exist(mrr_filename, 'file')
        try
            mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_range = ncread(mrr_filename, 'range');
            
            % 应用高度过滤
            mrr_height_idx = mrr_range <= 2500;
            mrr_range_filtered = mrr_range(mrr_height_idx);
            mrr_Za_filtered = mrr_Za(mrr_height_idx, :);
            
            % 找到最接近的时间点
            [~, mrr_time_idx] = min(abs(mrr_time - target_time));
            mrr_actual_time = mrr_time(mrr_time_idx);
            
            fprintf('   MRR时间: %s (差异: %.1f分钟)\n', ...
                datestr(mrr_actual_time), (mrr_actual_time - target_time) * 24 * 60);
            
            % 分析MRR数据
            mrr_profile = mrr_Za_filtered(:, mrr_time_idx);
            mrr_valid = ~isnan(mrr_profile) & mrr_profile > -30;
            
            if any(mrr_valid)
                mrr_max_height = max(mrr_range_filtered(mrr_valid));
                fprintf('   MRR最大有效高度: %.0f m (%d层)\n', mrr_max_height, sum(mrr_valid));
            else
                fprintf('   MRR无有效数据\n');
            end
            
        catch ME
            fprintf('   MRR数据读取错误: %s\n', ME.message);
        end
    else
        fprintf('   MRR文件不存在\n');
    end
    
    %% 查找云雷达数据 (使用UTC时间)
    [~, radar_time_idx] = min(abs(obs_time_utc - target_time));
    radar_actual_time = obs_time_utc(radar_time_idx);
    
    fprintf('   云雷达UTC时间: %s (差异: %.1f分钟)\n', ...
        datestr(radar_actual_time), (radar_actual_time - target_time) * 24 * 60);
    fprintf('   云雷达BJT时间: %s\n', datestr(radar_actual_time + 8/24));
    
    % 分析Ka和W数据
    ka_profile = ka_data_filtered(:, radar_time_idx);
    w_profile = w_data_filtered(:, radar_time_idx);
    
    ka_valid = ~isnan(ka_profile) & ka_profile > -30;
    w_valid = ~isnan(w_profile) & w_profile > -30;
    
    if any(ka_valid)
        ka_max_height = max(range_ka_filtered(ka_valid));
        fprintf('   Ka最大有效高度: %.0f m (%d层)\n', ka_max_height, sum(ka_valid));
    else
        fprintf('   Ka无有效数据\n');
    end
    
    if any(w_valid)
        w_max_height = max(range_ka_filtered(w_valid));
        fprintf('   W最大有效高度: %.0f m (%d层)\n', w_max_height, sum(w_valid));
    else
        fprintf('   W无有效数据\n');
    end
end

%% 3. 创建时区修正后的CSV导出脚本建议
fprintf('\n\n3. 时区修正建议\n');
fprintf('发现的问题:\n');
fprintf('  - MRR数据使用UTC时间\n');
fprintf('  - 云雷达数据使用BJT时间 (UTC+8)\n');
fprintf('  - 之前的对比分析存在8小时时差\n');
fprintf('\n解决方案:\n');
fprintf('  1. 重新导出CSV时统一使用UTC时间\n');
fprintf('  2. 云雷达时间减去8小时转为UTC\n');
fprintf('  3. 确保时间对齐后再进行数据对比\n');

%% 4. 验证时区修正效果
fprintf('\n4. 验证时区修正效果\n');

% 选择一个时间点验证
test_time_utc = datenum('2025-01-08 12:00:00', 'yyyy-mm-dd HH:MM:SS');
test_time_bjt = test_time_utc + 8/24;

fprintf('测试时间点:\n');
fprintf('  UTC: %s\n', datestr(test_time_utc));
fprintf('  BJT: %s\n', datestr(test_time_bjt));

% 查找对应的数据
[~, radar_idx] = min(abs(obs_time_utc - test_time_utc));
[~, radar_idx_bjt] = min(abs(obs_time_bjt - test_time_bjt));

fprintf('\n使用UTC时间匹配:\n');
fprintf('  云雷达UTC时间: %s\n', datestr(obs_time_utc(radar_idx)));
fprintf('  时间差: %.1f分钟\n', (obs_time_utc(radar_idx) - test_time_utc) * 24 * 60);

fprintf('\n使用BJT时间匹配:\n');
fprintf('  云雷达BJT时间: %s\n', datestr(obs_time_bjt(radar_idx_bjt)));
fprintf('  时间差: %.1f分钟\n', (obs_time_bjt(radar_idx_bjt) - test_time_bjt) * 24 * 60);

fprintf('\n=== 时区分析完成 ===\n');
fprintf('建议: 重新生成时区修正后的CSV文件\n');
