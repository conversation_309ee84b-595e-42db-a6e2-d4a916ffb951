clear
close all
clc

%% Batch plotting script for MRR data - January 8, 2025
% This script generates individual plots for each hour of MRR data

% Set data directory and date
data_dir = '20250108';
date_str = '20250108';

% Create output directory for plots
output_dir = 'plots_20250108';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
    fprintf('Created output directory: %s\n', output_dir);
end

% Get all nc files
nc_files = dir(fullfile(data_dir, '*.nc'));
fprintf('Found %d NetCDF files to process\n', length(nc_files));

%% Process each file and generate plots
for i = 1:length(nc_files)
    filename = fullfile(data_dir, nc_files(i).name);
    fprintf('Processing file %d/%d: %s\n', i, length(nc_files), nc_files(i).name);
    
    try
        % Extract hour from filename for title
        [~, base_name, ~] = fileparts(nc_files(i).name);
        hour_str = base_name(end-5:end-4);  % Extract hour from filename like "20250108_120000"
        
        % Read nc file data
        infonc = ncinfo(filename);
        lat = ncread(filename, 'latitude');
        lon = ncread(filename, 'longitude');
        Za = ncread(filename, 'Za');
        Z = ncread(filename, 'Z');
        RR = ncread(filename, 'RR');
        range = ncread(filename, 'range');
        time = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        % Create figure
        figure('Position', [100, 100, 1200, 900]);
        
        % Plot 1: Reflectivity factor Za
        subplot(2,2,1)
        pcolor(time, range/1000, Za)
        shading flat
        clb1 = colorbar;
        ylim([0 4])
        caxis([-20 20])
        datetick('x', 'HH:MM')
        ylabel('Height [km]')
        xlabel('Time [UTC]')
        ylabel(clb1, 'Reflectivity Factor [dBZ]')
        title(sprintf('Reflectivity Factor Za - %s:00 UTC', hour_str))
        set(gca, 'FontSize', 12)
        
        % Plot 2: Equivalent reflectivity factor Z
        subplot(2,2,2)
        pcolor(time, range/1000, Z)
        shading flat
        clb2 = colorbar;
        ylim([0 4])
        caxis([-20 20])
        datetick('x', 'HH:MM')
        ylabel('Height [km]')
        xlabel('Time [UTC]')
        ylabel(clb2, 'Equivalent Reflectivity Factor [dBZ]')
        title(sprintf('Equivalent Reflectivity Factor Z - %s:00 UTC', hour_str))
        set(gca, 'FontSize', 12)
        
        % Plot 3: Rain rate
        subplot(2,2,3)
        pcolor(time, range/1000, log10(RR + 0.01))  % Log scale to handle zero values
        shading flat
        clb3 = colorbar;
        ylim([0 4])
        datetick('x', 'HH:MM')
        ylabel('Height [km]')
        xlabel('Time [UTC]')
        ylabel(clb3, 'log10(Rain Rate) [mm/h]')
        title(sprintf('Rain Rate RR - %s:00 UTC', hour_str))
        set(gca, 'FontSize', 12)
        
        % Plot 4: Vertical profile (average over time)
        subplot(2,2,4)
        mean_Za = nanmean(Za, 2);
        mean_Z = nanmean(Z, 2);
        mean_RR = nanmean(RR, 2);
        
        plot(mean_Za, range/1000, 'b-', 'LineWidth', 2, 'DisplayName', 'Za')
        hold on
        plot(mean_Z, range/1000, 'r--', 'LineWidth', 2, 'DisplayName', 'Z')
        ylabel('Height [km]')
        xlabel('Reflectivity [dBZ]')
        title(sprintf('Average Vertical Profile - %s:00 UTC', hour_str))
        legend('Location', 'best')
        grid on
        ylim([0 4])
        xlim([-20 20])
        set(gca, 'FontSize', 12)
        
        % Add main title
        sgtitle(sprintf('MRR Data Analysis - January 8, 2025, %s:00 UTC\nLat: %.4f°, Lon: %.4f°', ...
                hour_str, lat, lon), 'FontSize', 14, 'FontWeight', 'bold')
        
        % Save figure
        output_filename = fullfile(output_dir, sprintf('MRR_%s_%s00', date_str, hour_str));
        print(output_filename, '-dpng', '-r300')
        fprintf('  Saved: %s.png\n', output_filename);
        
        % Close figure to save memory
        close(gcf);
        
    catch ME
        fprintf('  Error processing %s: %s\n', nc_files(i).name, ME.message);
        continue;
    end
end

fprintf('\nBatch processing complete!\n');
fprintf('Generated %d plots in directory: %s\n', length(nc_files), output_dir);

%% Create summary plot with all 24 hours
fprintf('Creating 24-hour summary plot...\n');

% Initialize arrays for summary
all_time_summary = [];
all_Za_summary = [];

% Read all files for summary
for i = 1:length(nc_files)
    filename = fullfile(data_dir, nc_files(i).name);
    try
        Za_temp = ncread(filename, 'Za');
        time_temp = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        if i == 1
            range = ncread(filename, 'range');
            lat = ncread(filename, 'latitude');
            lon = ncread(filename, 'longitude');
        end
        
        all_time_summary = [all_time_summary; time_temp];
        if isempty(all_Za_summary)
            all_Za_summary = Za_temp;
        else
            all_Za_summary = [all_Za_summary, Za_temp];
        end
    catch ME
        fprintf('Error in summary for %s: %s\n', nc_files(i).name, ME.message);
    end
end

% Create 24-hour summary plot
figure('Position', [100, 100, 1400, 600]);
pcolor(all_time_summary, range/1000, all_Za_summary)
shading flat
clb = colorbar;
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
ylabel(clb, 'Reflectivity Factor [dBZ]')
title(sprintf('MRR 24-Hour Summary - January 8, 2025\nLat: %.4f°, Lon: %.4f°', lat, lon))
set(gca, 'FontSize', 15)

% Save summary plot
summary_filename = fullfile(output_dir, 'MRR_24hour_summary_20250108');
print(summary_filename, '-dpng', '-r300')
fprintf('24-hour summary saved as: %s.png\n', summary_filename);

fprintf('\nAll plots generated successfully!\n');
fprintf('Individual hourly plots: %s/MRR_20250108_*.png\n', output_dir);
fprintf('24-hour summary: %s.png\n', summary_filename);
