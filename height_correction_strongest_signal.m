clear
close all
clc

%% 基于共同时间段最强信号点的高度校正分析
% 找到MRR和云雷达重合时间内反射率最强的时刻进行高度校正

fprintf('=== 基于最强信号的高度校正分析 ===\n');
fprintf('方法: 找到重合时间段内反射率最强的时刻进行校正\n\n');

%% 1. 加载云雷达数据
fprintf('1. 加载云雷达数据\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;
    obs_time_utc = obs_time_bjt - 8/24;  % BJT转UTC
    
    range_ka_original = shandong_data.range_ka;
    if max(range_ka_original) < 100
        range_ka_original = range_ka_original * 1000;
    end
    
    ka_ze = shandong_data.ze.ka;
    w_ze = shandong_data.ze.w;
    ka_vel = shandong_data.vel.ka;
    w_vel = shandong_data.vel.w;
    
    fprintf('   云雷达UTC时间范围: %s - %s\n', ...
        datestr(min(obs_time_utc)), datestr(max(obs_time_utc)));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载MRR重合时间段数据
fprintf('\n2. 加载MRR重合时间段数据\n');

radar_start_utc = min(obs_time_utc);
radar_end_utc = max(obs_time_utc);

% 收集所有重合的MRR数据
all_mrr_time = [];
all_mrr_Za = [];
all_mrr_VEL = [];
all_mrr_range = [];

mrr_files = dir('20250108/*.nc');
for i = 1:length(mrr_files)
    mrr_filename = fullfile('20250108', mrr_files(i).name);
    try
        mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        % 检查是否与云雷达时间重合
        if max(mrr_time) >= radar_start_utc && min(mrr_time) <= radar_end_utc
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_VEL = ncread(mrr_filename, 'VEL');
            mrr_range = ncread(mrr_filename, 'range');
            
            % 找到重合的时间段
            overlap_mask = mrr_time >= radar_start_utc & mrr_time <= radar_end_utc;
            
            if any(overlap_mask)
                if isempty(all_mrr_time)
                    all_mrr_time = mrr_time(overlap_mask);
                    all_mrr_Za = mrr_Za(:, overlap_mask);
                    all_mrr_VEL = mrr_VEL(:, overlap_mask);
                    all_mrr_range = mrr_range;
                else
                    all_mrr_time = [all_mrr_time; mrr_time(overlap_mask)];
                    all_mrr_Za = [all_mrr_Za, mrr_Za(:, overlap_mask)];
                    all_mrr_VEL = [all_mrr_VEL, mrr_VEL(:, overlap_mask)];
                end
            end
        end
    catch
        continue;
    end
end

fprintf('   MRR重合数据: %d个时间点\n', length(all_mrr_time));
fprintf('   MRR重合时间范围: %s - %s\n', ...
    datestr(min(all_mrr_time)), datestr(max(all_mrr_time)));

%% 3. 找到最强信号时刻
fprintf('\n3. 寻找最强信号时刻\n');

% 计算每个时刻的平均反射率强度（2km以下）
mrr_2km_mask = all_mrr_range <= 2000;
radar_2km_mask = range_ka_original <= 2000;

mrr_strength = zeros(size(all_mrr_time));
ka_strength = zeros(size(obs_time_utc));
w_strength = zeros(size(obs_time_utc));

% MRR强度计算
for t = 1:length(all_mrr_time)
    profile = all_mrr_Za(mrr_2km_mask, t);
    valid_data = ~isnan(profile) & profile > -20;
    if any(valid_data)
        mrr_strength(t) = mean(profile(valid_data));
    else
        mrr_strength(t) = -999;
    end
end

% 云雷达强度计算
for t = 1:length(obs_time_utc)
    ka_profile = ka_ze(radar_2km_mask, t);
    w_profile = w_ze(radar_2km_mask, t);
    
    ka_valid = ~isnan(ka_profile) & ka_profile > -20;
    w_valid = ~isnan(w_profile) & w_profile > -20;
    
    if any(ka_valid)
        ka_strength(t) = mean(ka_profile(ka_valid));
    else
        ka_strength(t) = -999;
    end
    
    if any(w_valid)
        w_strength(t) = mean(w_profile(w_valid));
    else
        w_strength(t) = -999;
    end
end

% 找到重合时间段内的最强时刻
overlap_start = max(radar_start_utc, min(all_mrr_time));
overlap_end = min(radar_end_utc, max(all_mrr_time));

mrr_overlap_mask = all_mrr_time >= overlap_start & all_mrr_time <= overlap_end;
radar_overlap_mask = obs_time_utc >= overlap_start & obs_time_utc <= overlap_end;

mrr_strength_overlap = mrr_strength(mrr_overlap_mask);
mrr_time_overlap = all_mrr_time(mrr_overlap_mask);

% 找到MRR最强时刻
[max_mrr_strength, max_mrr_idx] = max(mrr_strength_overlap);
strongest_time_mrr = mrr_time_overlap(max_mrr_idx);

fprintf('   MRR最强信号时刻: %s (强度: %.1f dBZ)\n', ...
    datestr(strongest_time_mrr), max_mrr_strength);

% 找到云雷达对应的最接近时刻
[~, closest_radar_idx] = min(abs(obs_time_utc - strongest_time_mrr));
closest_radar_time = obs_time_utc(closest_radar_idx);

fprintf('   云雷达对应时刻: %s (时差: %.1f分钟)\n', ...
    datestr(closest_radar_time), (closest_radar_time - strongest_time_mrr) * 24 * 60);

%% 4. 提取最强时刻的垂直剖面
fprintf('\n4. 提取最强时刻的垂直剖面\n');

% MRR数据
mrr_global_idx = find(all_mrr_time == strongest_time_mrr);
mrr_za_strongest = all_mrr_Za(:, mrr_global_idx);
mrr_vel_strongest = all_mrr_VEL(:, mrr_global_idx);

% 云雷达数据
ka_ze_strongest = ka_ze(:, closest_radar_idx);
w_ze_strongest = w_ze(:, closest_radar_idx);
ka_vel_strongest = ka_vel(:, closest_radar_idx);
w_vel_strongest = w_vel(:, closest_radar_idx);

% 限制到2km以下
mrr_za_2km = mrr_za_strongest(mrr_2km_mask);
mrr_vel_2km = mrr_vel_strongest(mrr_2km_mask);
mrr_heights_2km = all_mrr_range(mrr_2km_mask);

ka_ze_2km = ka_ze_strongest(radar_2km_mask);
w_ze_2km = w_ze_strongest(radar_2km_mask);
ka_vel_2km = ka_vel_strongest(radar_2km_mask);
w_vel_2km = w_vel_strongest(radar_2km_mask);
radar_heights_2km = range_ka_original(radar_2km_mask);

fprintf('   MRR 2km以下数据点: %d\n', length(mrr_za_2km));
fprintf('   云雷达 2km以下数据点: %d\n', length(ka_ze_2km));

%% 5. 基于最强信号的高度校正
fprintf('\n5. 基于最强信号的高度校正\n');

% 使用更精细的偏移搜索
height_offsets = -300:5:100;  % 测试-300到+100m，步长5m
correlation_za_ka = zeros(size(height_offsets));
correlation_za_w = zeros(size(height_offsets));
correlation_vel_ka = zeros(size(height_offsets));
correlation_vel_w = zeros(size(height_offsets));

% 通用高度网格
common_heights = 200:10:2000;  % 10m分辨率

% MRR插值到通用网格
mrr_za_interp = interp1(mrr_heights_2km, mrr_za_2km, common_heights, 'linear', NaN);
mrr_vel_interp = interp1(mrr_heights_2km, mrr_vel_2km, common_heights, 'linear', NaN);

for i = 1:length(height_offsets)
    offset = height_offsets(i);
    
    % 应用偏移
    adjusted_radar_heights = radar_heights_2km + offset;
    
    % 插值云雷达数据
    ka_ze_interp = interp1(adjusted_radar_heights, ka_ze_2km, common_heights, 'linear', NaN);
    w_ze_interp = interp1(adjusted_radar_heights, w_ze_2km, common_heights, 'linear', NaN);
    ka_vel_interp = interp1(adjusted_radar_heights, ka_vel_2km, common_heights, 'linear', NaN);
    w_vel_interp = interp1(adjusted_radar_heights, w_vel_2km, common_heights, 'linear', NaN);
    
    % 计算相关系数
    valid_za_ka = ~isnan(mrr_za_interp) & ~isnan(ka_ze_interp);
    valid_za_w = ~isnan(mrr_za_interp) & ~isnan(w_ze_interp);
    valid_vel_ka = ~isnan(mrr_vel_interp) & ~isnan(ka_vel_interp);
    valid_vel_w = ~isnan(mrr_vel_interp) & ~isnan(w_vel_interp);
    
    if sum(valid_za_ka) > 20
        corr_temp = corrcoef(mrr_za_interp(valid_za_ka), ka_ze_interp(valid_za_ka));
        correlation_za_ka(i) = corr_temp(1,2);
    end
    
    if sum(valid_za_w) > 20
        corr_temp = corrcoef(mrr_za_interp(valid_za_w), w_ze_interp(valid_za_w));
        correlation_za_w(i) = corr_temp(1,2);
    end
    
    if sum(valid_vel_ka) > 20
        corr_temp = corrcoef(mrr_vel_interp(valid_vel_ka), abs(ka_vel_interp(valid_vel_ka)));
        correlation_vel_ka(i) = corr_temp(1,2);
    end
    
    if sum(valid_vel_w) > 20
        corr_temp = corrcoef(mrr_vel_interp(valid_vel_w), abs(w_vel_interp(valid_vel_w)));
        correlation_vel_w(i) = corr_temp(1,2);
    end
end

% 找到最佳校正
[max_corr_za_ka, max_idx_za_ka] = max(correlation_za_ka);
[max_corr_za_w, max_idx_za_w] = max(correlation_za_w);
[max_corr_vel_ka, max_idx_vel_ka] = max(correlation_vel_ka);
[max_corr_vel_w, max_idx_vel_w] = max(correlation_vel_w);

best_offset_za_ka = height_offsets(max_idx_za_ka);
best_offset_za_w = height_offsets(max_idx_za_w);
best_offset_vel_ka = height_offsets(max_idx_vel_ka);
best_offset_vel_w = height_offsets(max_idx_vel_w);

fprintf('基于最强信号时刻的校正结果:\n');
fprintf('  分析时刻: %s UTC (MRR最强信号)\n', datestr(strongest_time_mrr));
fprintf('  对应云雷达时刻: %s UTC\n', datestr(closest_radar_time));

fprintf('\n反射率匹配校正:\n');
fprintf('  Ka波段: %+.0f m (相关系数: %.3f)\n', best_offset_za_ka, max_corr_za_ka);
fprintf('  W波段: %+.0f m (相关系数: %.3f)\n', best_offset_za_w, max_corr_za_w);

fprintf('\n速度匹配校正:\n');
fprintf('  Ka波段: %+.0f m (相关系数: %.3f)\n', best_offset_vel_ka, max_corr_vel_ka);
fprintf('  W波段: %+.0f m (相关系数: %.3f)\n', best_offset_vel_w, max_corr_vel_w);

% 综合校正
final_offset_ka = (best_offset_za_ka + best_offset_vel_ka) / 2;
final_offset_w = (best_offset_za_w + best_offset_vel_w) / 2;

fprintf('\n最终校正建议 (基于最强信号):\n');
fprintf('  Ka波段: %+.0f m\n', final_offset_ka);
fprintf('  W波段: %+.0f m\n', final_offset_w);

%% 6. 可视化最强信号时刻的校正效果
fprintf('\n6. 可视化最强信号时刻的校正效果\n');

figure('Position', [100, 100, 1600, 1000]);

% 应用最终校正
range_ka_corrected = range_ka_original + final_offset_ka;
range_w_corrected = range_ka_original + final_offset_w;

% 子图1: 反射率垂直剖面对比
subplot(2,3,1)
plot(mrr_za_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR Za (参考)')
hold on
plot(ka_ze_2km, radar_heights_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka (校正前)')
plot(ka_ze_2km, (radar_heights_2km + final_offset_ka)/1000, 'r-', 'LineWidth', 2, 'DisplayName', sprintf('Ka (校正%+.0fm)', final_offset_ka))
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('Ka波段高度校正 - 反射率')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([-25 15])

subplot(2,3,2)
plot(mrr_za_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR Za (参考)')
hold on
plot(w_ze_2km, radar_heights_2km/1000, 'c--', 'LineWidth', 2, 'DisplayName', 'W (校正前)')
plot(w_ze_2km, (radar_heights_2km + final_offset_w)/1000, 'm-', 'LineWidth', 2, 'DisplayName', sprintf('W (校正%+.0fm)', final_offset_w))
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('W波段高度校正 - 反射率')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([-25 15])

% 子图2: 速度垂直剖面对比
subplot(2,3,3)
plot(mrr_vel_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR VEL (参考)')
hold on
plot(abs(ka_vel_2km), radar_heights_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka (校正前)')
plot(abs(ka_vel_2km), (radar_heights_2km + final_offset_ka)/1000, 'r-', 'LineWidth', 2, 'DisplayName', sprintf('Ka (校正%+.0fm)', final_offset_ka))
ylabel('高度 [km]')
xlabel('速度 [m/s]')
title('Ka波段高度校正 - 速度')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([0 8])

subplot(2,3,4)
plot(mrr_vel_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR VEL (参考)')
hold on
plot(abs(w_vel_2km), radar_heights_2km/1000, 'c--', 'LineWidth', 2, 'DisplayName', 'W (校正前)')
plot(abs(w_vel_2km), (radar_heights_2km + final_offset_w)/1000, 'm-', 'LineWidth', 2, 'DisplayName', sprintf('W (校正%+.0fm)', final_offset_w))
ylabel('高度 [km]')
xlabel('速度 [m/s]')
title('W波段高度校正 - 速度')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([0 8])

% 子图3: 相关系数曲线
subplot(2,3,5)
plot(height_offsets, correlation_za_ka, 'g-', 'LineWidth', 2, 'DisplayName', 'Ka反射率')
hold on
plot(height_offsets, correlation_vel_ka, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka速度')
plot(best_offset_za_ka, max_corr_za_ka, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
plot(best_offset_vel_ka, max_corr_vel_ka, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
plot(final_offset_ka, 0.5, 'ks', 'MarkerSize', 10, 'MarkerFaceColor', 'k', 'DisplayName', '最终校正')
ylabel('相关系数')
xlabel('高度偏移 [m]')
title('Ka波段校正优化曲线')
legend('Location', 'best')
grid on

subplot(2,3,6)
plot(height_offsets, correlation_za_w, 'm-', 'LineWidth', 2, 'DisplayName', 'W反射率')
hold on
plot(height_offsets, correlation_vel_w, 'm--', 'LineWidth', 2, 'DisplayName', 'W速度')
plot(best_offset_za_w, max_corr_za_w, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
plot(best_offset_vel_w, max_corr_vel_w, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
plot(final_offset_w, 0.5, 'ks', 'MarkerSize', 10, 'MarkerFaceColor', 'k', 'DisplayName', '最终校正')
ylabel('相关系数')
xlabel('高度偏移 [m]')
title('W波段校正优化曲线')
legend('Location', 'best')
grid on

sgtitle(sprintf('基于最强信号的高度校正分析\n时刻: %s UTC', datestr(strongest_time_mrr)), ...
    'FontSize', 14, 'FontWeight', 'bold')

% 保存图片
print('height_correction_strongest_signal', '-dpng', '-r300')
fprintf('   最强信号校正图已保存: height_correction_strongest_signal.png\n');

%% 7. 验证校正效果
fprintf('\n7. 校正效果验证\n');

% 计算校正前后的特征匹配度
fprintf('校正前后特征对比:\n');

% 找到特征峰值
[mrr_peaks, mrr_locs] = findpeaks(mrr_za_2km, 'MinPeakHeight', -5, 'MinPeakDistance', 3);
[ka_peaks, ka_locs] = findpeaks(ka_ze_2km, 'MinPeakHeight', -15, 'MinPeakDistance', 2);
[w_peaks, w_locs] = findpeaks(w_ze_2km, 'MinPeakHeight', -15, 'MinPeakDistance', 2);

if ~isempty(mrr_peaks) && ~isempty(ka_peaks)
    mrr_peak_heights = mrr_heights_2km(mrr_locs);
    ka_peak_heights_orig = radar_heights_2km(ka_locs);
    ka_peak_heights_corr = ka_peak_heights_orig + final_offset_ka;
    
    fprintf('  MRR主要峰值高度: ');
    for i = 1:min(3, length(mrr_peak_heights))
        fprintf('%.0fm ', mrr_peak_heights(i));
    end
    fprintf('\n');
    
    fprintf('  Ka校正前峰值高度: ');
    for i = 1:min(3, length(ka_peak_heights_orig))
        fprintf('%.0fm ', ka_peak_heights_orig(i));
    end
    fprintf('\n');
    
    fprintf('  Ka校正后峰值高度: ');
    for i = 1:min(3, length(ka_peak_heights_corr))
        fprintf('%.0fm ', ka_peak_heights_corr(i));
    end
    fprintf('\n');
end

fprintf('\n=== 基于最强信号的高度校正完成 ===\n');
fprintf('推荐校正值:\n');
fprintf('  Ka波段高度 = 原始高度 %+.0f m\n', final_offset_ka);
fprintf('  W波段高度 = 原始高度 %+.0f m\n', final_offset_w);
