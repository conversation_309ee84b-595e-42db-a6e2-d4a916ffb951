clear
close all
clc

%% 检查MRR数据中是否包含速度信息

fprintf('=== 检查MRR数据中的速度信息 ===\n\n');

%% 1. 检查几个MRR文件的变量结构
mrr_files_to_check = {
    '20250108/20250108_120000.nc',
    '20250108/20250108_060000.nc',
    '20250108/20250108_200000.nc'
};

for i = 1:length(mrr_files_to_check)
    filename = mrr_files_to_check{i};
    fprintf('检查文件: %s\n', filename);
    
    if exist(filename, 'file')
        try
            % 获取文件信息
            info = ncinfo(filename);
            
            fprintf('  文件中的变量:\n');
            for j = 1:length(info.Variables)
                var = info.Variables(j);
                fprintf('    %s: %s', var.Name, mat2str(var.Size));
                
                % 检查变量属性
                if ~isempty(var.Attributes)
                    for k = 1:length(var.Attributes)
                        attr = var.Attributes(k);
                        if strcmp(attr.Name, 'long_name') || strcmp(attr.Name, 'units') || strcmp(attr.Name, 'standard_name')
                            fprintf(' (%s: %s)', attr.Name, attr.Value);
                        end
                    end
                end
                fprintf('\n');
                
                % 特别检查可能的速度变量
                var_name_lower = lower(var.Name);
                if contains(var_name_lower, 'vel') || contains(var_name_lower, 'speed') || ...
                   contains(var_name_lower, 'doppler') || strcmp(var.Name, 'V') || strcmp(var.Name, 'v')
                    fprintf('    *** 可能的速度变量: %s ***\n', var.Name);
                end
            end
            
            % 尝试读取常见的速度变量名
            possible_vel_vars = {'V', 'v', 'vel', 'velocity', 'Vel', 'Velocity', 'doppler_velocity', 'radial_velocity'};
            
            fprintf('  检查可能的速度变量:\n');
            for j = 1:length(possible_vel_vars)
                var_name = possible_vel_vars{j};
                try
                    test_data = ncread(filename, var_name);
                    fprintf('    找到速度变量: %s, 维度: %s\n', var_name, mat2str(size(test_data)));
                    
                    % 显示一些统计信息
                    fprintf('      数值范围: %.3f 到 %.3f\n', min(test_data(:)), max(test_data(:)));
                    fprintf('      NaN比例: %.1f%%\n', sum(isnan(test_data(:)))/numel(test_data)*100);
                    
                catch
                    % 变量不存在，继续检查下一个
                end
            end
            
        catch ME
            fprintf('  读取文件信息错误: %s\n', ME.message);
        end
    else
        fprintf('  文件不存在\n');
    end
    fprintf('\n');
end

%% 2. 详细检查一个文件的所有变量属性
fprintf('2. 详细检查文件变量属性\n');
test_file = '20250108/20250108_120000.nc';

if exist(test_file, 'file')
    fprintf('详细分析文件: %s\n', test_file);
    
    try
        info = ncinfo(test_file);
        
        fprintf('\n所有变量的详细属性:\n');
        for i = 1:length(info.Variables)
            var = info.Variables(i);
            fprintf('\n变量: %s\n', var.Name);
            fprintf('  维度: %s\n', mat2str(var.Size));
            fprintf('  数据类型: %s\n', var.Datatype);
            
            if ~isempty(var.Attributes)
                fprintf('  属性:\n');
                for j = 1:length(var.Attributes)
                    attr = var.Attributes(j);
                    fprintf('    %s: %s\n', attr.Name, attr.Value);
                end
            end
            
            % 如果变量名可能是速度，尝试读取一些数据
            if length(var.Name) <= 3 && ~ismember(var.Name, {'Za', 'Z', 'RR', 'lat', 'lon', 'latitude', 'longitude', 'time', 'range'})
                try
                    sample_data = ncread(test_file, var.Name);
                    if isnumeric(sample_data) && length(var.Size) >= 2
                        fprintf('  *** 可能的速度数据 ***\n');
                        fprintf('    数值范围: %.3f 到 %.3f\n', min(sample_data(:)), max(sample_data(:)));
                        fprintf('    平均值: %.3f\n', mean(sample_data(:), 'omitnan'));
                        fprintf('    标准差: %.3f\n', std(sample_data(:), 'omitnan'));
                    end
                catch
                    % 读取失败，跳过
                end
            end
        end
        
    catch ME
        fprintf('详细分析错误: %s\n', ME.message);
    end
end

%% 3. 总结
fprintf('\n\n=== MRR速度数据检查总结 ===\n');
fprintf('检查结果:\n');
fprintf('  - 已检查多个MRR文件的变量结构\n');
fprintf('  - 查找了常见的速度变量名\n');
fprintf('  - 分析了变量属性和数据特征\n');
fprintf('\n如果找到速度变量，将在上面的输出中标注\n');
fprintf('如果未找到，说明MRR数据可能不包含速度信息\n');

fprintf('\n=== 检查完成 ===\n');
