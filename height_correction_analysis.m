clear
close all
clc

%% Ka和W波段高度bins校正分析
% 使用MRR作为参考，通过反射率和速度模式匹配来校正Ka/W的高度设置

fprintf('=== Ka和W波段高度bins校正分析 ===\n');
fprintf('参考标准: MRR高度数据 (UTC时间)\n');
fprintf('校正目标: Ka/W波段高度bins (BJT→UTC时间)\n\n');

%% 1. 加载数据
fprintf('1. 加载数据\n');

% 加载云雷达数据
shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;
    obs_time_utc = obs_time_bjt - 8/24;  % BJT转UTC
    
    range_ka_original = shandong_data.range_ka;
    if max(range_ka_original) < 100
        range_ka_original = range_ka_original * 1000;  % 转换为米
    end
    
    ka_ze = shandong_data.ze.ka;
    w_ze = shandong_data.ze.w;
    ka_vel = shandong_data.vel.ka;
    w_vel = shandong_data.vel.w;
    
    fprintf('   云雷达数据加载完成\n');
    fprintf('   原始高度范围: %.0f - %.0f m\n', min(range_ka_original), max(range_ka_original));
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

% 加载MRR数据 (选择几个代表性时间)
test_hours = [6, 12, 18];  % 选择早上、中午、晚上
mrr_data_collection = struct();

for h = 1:length(test_hours)
    hour = test_hours(h);
    mrr_filename = sprintf('20250108/20250108_%02d0000.nc', hour);
    
    if exist(mrr_filename, 'file')
        try
            mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_VEL = ncread(mrr_filename, 'VEL');
            mrr_range = ncread(mrr_filename, 'range');
            
            mrr_data_collection(h).time = mrr_time;
            mrr_data_collection(h).Za = mrr_Za;
            mrr_data_collection(h).VEL = mrr_VEL;
            mrr_data_collection(h).range = mrr_range;
            mrr_data_collection(h).hour = hour;
            
            fprintf('   MRR %02d:00数据加载完成\n', hour);
            
        catch ME
            fprintf('   MRR %02d:00数据加载失败: %s\n', hour, ME.message);
        end
    end
end

%% 2. 高度校正分析
fprintf('\n2. 高度校正分析\n');

% 选择一个时间段进行详细分析
target_time_utc = datenum('2025-01-08 12:00:00', 'yyyy-mm-dd HH:MM:SS');

% 找到MRR数据
mrr_idx = find([mrr_data_collection.hour] == 12);
if ~isempty(mrr_idx)
    mrr_data = mrr_data_collection(mrr_idx);
    [~, mrr_time_idx] = min(abs(mrr_data.time - target_time_utc));
    
    mrr_profile_za = mrr_data.Za(:, mrr_time_idx);
    mrr_profile_vel = mrr_data.VEL(:, mrr_time_idx);
    mrr_heights = mrr_data.range;
    
    fprintf('   MRR参考时间: %s\n', datestr(mrr_data.time(mrr_time_idx)));
end

% 找到云雷达数据
[~, radar_time_idx] = min(abs(obs_time_utc - target_time_utc));
ka_profile = ka_ze(:, radar_time_idx);
w_profile = w_ze(:, radar_time_idx);
ka_vel_profile = ka_vel(:, radar_time_idx);
w_vel_profile = w_vel(:, radar_time_idx);

fprintf('   云雷达参考时间: %s\n', datestr(obs_time_utc(radar_time_idx)));

%% 3. 特征匹配分析
fprintf('\n3. 特征匹配分析\n');

% 限制分析范围到2km以下
mrr_mask = mrr_heights <= 2000;
radar_mask = range_ka_original <= 2000;

mrr_heights_2km = mrr_heights(mrr_mask);
mrr_za_2km = mrr_profile_za(mrr_mask);
mrr_vel_2km = mrr_profile_vel(mrr_mask);

range_ka_2km = range_ka_original(radar_mask);
ka_ze_2km = ka_profile(radar_mask);
w_ze_2km = w_profile(radar_mask);
ka_vel_2km = ka_vel_profile(radar_mask);
w_vel_2km = w_vel_profile(radar_mask);

% 寻找反射率峰值进行匹配
[mrr_peaks, mrr_peak_locs] = findpeaks(mrr_za_2km, 'MinPeakHeight', -10, 'MinPeakDistance', 5);
[ka_peaks, ka_peak_locs] = findpeaks(ka_ze_2km, 'MinPeakHeight', -20, 'MinPeakDistance', 3);
[w_peaks, w_peak_locs] = findpeaks(w_ze_2km, 'MinPeakHeight', -20, 'MinPeakDistance', 3);

fprintf('   找到反射率峰值:\n');
fprintf('     MRR: %d个峰值\n', length(mrr_peaks));
fprintf('     Ka: %d个峰值\n', length(ka_peaks));
fprintf('     W: %d个峰值\n', length(w_peaks));

% 显示峰值高度
if ~isempty(mrr_peaks)
    mrr_peak_heights = mrr_heights_2km(mrr_peak_locs);
    fprintf('     MRR峰值高度: ');
    for i = 1:length(mrr_peak_heights)
        fprintf('%.0fm(%.1fdBZ) ', mrr_peak_heights(i), mrr_peaks(i));
    end
    fprintf('\n');
end

if ~isempty(ka_peaks)
    ka_peak_heights = range_ka_2km(ka_peak_locs);
    fprintf('     Ka峰值高度: ');
    for i = 1:length(ka_peak_heights)
        fprintf('%.0fm(%.1fdBZ) ', ka_peak_heights(i), ka_peaks(i));
    end
    fprintf('\n');
end

%% 4. 互相关分析进行高度校正
fprintf('\n4. 互相关分析\n');

% 将数据插值到相同的高度网格进行对比
common_heights = 0:20:2000;  % 使用20m间隔的通用网格

% 插值MRR数据到通用网格
mrr_za_interp = interp1(mrr_heights_2km, mrr_za_2km, common_heights, 'linear', NaN);
mrr_vel_interp = interp1(mrr_heights_2km, mrr_vel_2km, common_heights, 'linear', NaN);

% 尝试不同的高度偏移来找到最佳匹配
height_offsets = -200:10:200;  % 测试±200m的偏移，步长10m
correlation_za_ka = zeros(size(height_offsets));
correlation_za_w = zeros(size(height_offsets));
correlation_vel_ka = zeros(size(height_offsets));
correlation_vel_w = zeros(size(height_offsets));

for i = 1:length(height_offsets)
    offset = height_offsets(i);
    
    % 应用偏移后插值云雷达数据
    adjusted_ka_heights = range_ka_2km + offset;
    adjusted_w_heights = range_ka_2km + offset;
    
    ka_ze_interp = interp1(adjusted_ka_heights, ka_ze_2km, common_heights, 'linear', NaN);
    w_ze_interp = interp1(adjusted_w_heights, w_ze_2km, common_heights, 'linear', NaN);
    ka_vel_interp = interp1(adjusted_ka_heights, ka_vel_2km, common_heights, 'linear', NaN);
    w_vel_interp = interp1(adjusted_w_heights, w_vel_2km, common_heights, 'linear', NaN);
    
    % 计算相关系数（只使用有效数据）
    valid_mrr_ka = ~isnan(mrr_za_interp) & ~isnan(ka_ze_interp);
    valid_mrr_w = ~isnan(mrr_za_interp) & ~isnan(w_ze_interp);
    valid_vel_ka = ~isnan(mrr_vel_interp) & ~isnan(ka_vel_interp);
    valid_vel_w = ~isnan(mrr_vel_interp) & ~isnan(w_vel_interp);
    
    if sum(valid_mrr_ka) > 10
        corr_temp = corrcoef(mrr_za_interp(valid_mrr_ka), ka_ze_interp(valid_mrr_ka));
        correlation_za_ka(i) = corr_temp(1,2);
    else
        correlation_za_ka(i) = NaN;
    end
    
    if sum(valid_mrr_w) > 10
        corr_temp = corrcoef(mrr_za_interp(valid_mrr_w), w_ze_interp(valid_mrr_w));
        correlation_za_w(i) = corr_temp(1,2);
    else
        correlation_za_w(i) = NaN;
    end
    
    if sum(valid_vel_ka) > 10
        corr_temp = corrcoef(mrr_vel_interp(valid_vel_ka), abs(ka_vel_interp(valid_vel_ka)));
        correlation_vel_ka(i) = corr_temp(1,2);
    else
        correlation_vel_ka(i) = NaN;
    end
    
    if sum(valid_vel_w) > 10
        corr_temp = corrcoef(mrr_vel_interp(valid_vel_w), abs(w_vel_interp(valid_vel_w)));
        correlation_vel_w(i) = corr_temp(1,2);
    else
        correlation_vel_w(i) = NaN;
    end
end

%% 5. 找到最佳高度校正
fprintf('\n5. 最佳高度校正结果\n');

% 找到最大相关系数对应的偏移
[max_corr_za_ka, max_idx_za_ka] = max(correlation_za_ka);
[max_corr_za_w, max_idx_za_w] = max(correlation_za_w);
[max_corr_vel_ka, max_idx_vel_ka] = max(correlation_vel_ka);
[max_corr_vel_w, max_idx_vel_w] = max(correlation_vel_w);

best_offset_za_ka = height_offsets(max_idx_za_ka);
best_offset_za_w = height_offsets(max_idx_za_w);
best_offset_vel_ka = height_offsets(max_idx_vel_ka);
best_offset_vel_w = height_offsets(max_idx_vel_w);

fprintf('基于反射率匹配的最佳高度校正:\n');
fprintf('  Ka波段: %+.0f m (相关系数: %.3f)\n', best_offset_za_ka, max_corr_za_ka);
fprintf('  W波段: %+.0f m (相关系数: %.3f)\n', best_offset_za_w, max_corr_za_w);

fprintf('\n基于速度匹配的最佳高度校正:\n');
fprintf('  Ka波段: %+.0f m (相关系数: %.3f)\n', best_offset_vel_ka, max_corr_vel_ka);
fprintf('  W波段: %+.0f m (相关系数: %.3f)\n', best_offset_vel_w, max_corr_vel_w);

% 综合校正建议
avg_offset_ka = (best_offset_za_ka + best_offset_vel_ka) / 2;
avg_offset_w = (best_offset_za_w + best_offset_vel_w) / 2;

fprintf('\n综合校正建议:\n');
fprintf('  Ka波段高度校正: %+.0f m\n', avg_offset_ka);
fprintf('  W波段高度校正: %+.0f m\n', avg_offset_w);

%% 6. 可视化校正效果
fprintf('\n6. 创建校正效果可视化\n');

% 应用校正
range_ka_corrected = range_ka_original + avg_offset_ka;
range_w_corrected = range_ka_original + avg_offset_w;  % W使用相同的高度网格

figure('Position', [100, 100, 1600, 1200]);

% 子图1: 校正前后反射率对比
subplot(3,2,1)
plot(mrr_za_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR (参考)')
hold on
plot(ka_ze_2km, range_ka_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka (校正前)')
plot(ka_ze_2km, range_ka_corrected(radar_mask)/1000, 'r-', 'LineWidth', 2, 'DisplayName', 'Ka (校正后)')
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('Ka波段高度校正效果 - 反射率')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([-30 15])

subplot(3,2,2)
plot(mrr_za_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR (参考)')
hold on
plot(w_ze_2km, range_ka_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'W (校正前)')
plot(w_ze_2km, range_w_corrected(radar_mask)/1000, 'm-', 'LineWidth', 2, 'DisplayName', 'W (校正后)')
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('W波段高度校正效果 - 反射率')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([-30 15])

% 子图2: 校正前后速度对比
subplot(3,2,3)
plot(mrr_vel_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR (参考)')
hold on
plot(abs(ka_vel_2km), range_ka_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka (校正前)')
plot(abs(ka_vel_2km), range_ka_corrected(radar_mask)/1000, 'r-', 'LineWidth', 2, 'DisplayName', 'Ka (校正后)')
ylabel('高度 [km]')
xlabel('速度 [m/s]')
title('Ka波段高度校正效果 - 速度')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([0 8])

subplot(3,2,4)
plot(mrr_vel_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR (参考)')
hold on
plot(abs(w_vel_2km), range_ka_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'W (校正前)')
plot(abs(w_vel_2km), range_w_corrected(radar_mask)/1000, 'm-', 'LineWidth', 2, 'DisplayName', 'W (校正后)')
ylabel('高度 [km]')
xlabel('速度 [m/s]')
title('W波段高度校正效果 - 速度')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([0 8])

% 子图3: 相关系数随高度偏移的变化
subplot(3,2,5)
plot(height_offsets, correlation_za_ka, 'g-', 'LineWidth', 2, 'DisplayName', 'Ka反射率')
hold on
plot(height_offsets, correlation_vel_ka, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka速度')
plot(best_offset_za_ka, max_corr_za_ka, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
plot(best_offset_vel_ka, max_corr_vel_ka, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
ylabel('相关系数')
xlabel('高度偏移 [m]')
title('Ka波段最佳高度校正')
legend('Location', 'best')
grid on

subplot(3,2,6)
plot(height_offsets, correlation_za_w, 'm-', 'LineWidth', 2, 'DisplayName', 'W反射率')
hold on
plot(height_offsets, correlation_vel_w, 'm--', 'LineWidth', 2, 'DisplayName', 'W速度')
plot(best_offset_za_w, max_corr_za_w, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
plot(best_offset_vel_w, max_corr_vel_w, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r')
ylabel('相关系数')
xlabel('高度偏移 [m]')
title('W波段最佳高度校正')
legend('Location', 'best')
grid on

sgtitle('Ka和W波段高度bins校正分析', 'FontSize', 14, 'FontWeight', 'bold')

% 保存校正分析图
print('height_correction_analysis', '-dpng', '-r300')
fprintf('   校正分析图已保存: height_correction_analysis.png\n');

%% 7. 生成校正后的对比图
fprintf('\n7. 生成校正后的对比图\n');

% 使用校正后的高度重新生成时间-高度图
figure('Position', [100, 100, 1400, 1000]);

% 获取重合时间段的数据
overlap_start = max(min(obs_time_utc), datenum('2025-01-08 01:00:00', 'yyyy-mm-dd HH:MM:SS'));
overlap_end = min(max(obs_time_utc), datenum('2025-01-08 15:54:30', 'yyyy-mm-dd HH:MM:SS'));

% 子图1: 校正前后垂直剖面对比
subplot(2,1,1)
plot(mrr_za_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR Za (参考)')
hold on
plot(ka_ze_2km, range_ka_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka (校正前)')
plot(ka_ze_2km, range_ka_corrected(radar_mask)/1000, 'r-', 'LineWidth', 2, 'DisplayName', 'Ka (校正后)')
plot(w_ze_2km, range_ka_2km/1000, 'c--', 'LineWidth', 2, 'DisplayName', 'W (校正前)')
plot(w_ze_2km, range_w_corrected(radar_mask)/1000, 'm-', 'LineWidth', 2, 'DisplayName', 'W (校正后)')
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('高度校正效果对比 - 反射率剖面')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([-30 15])

% 子图2: 速度校正效果对比
subplot(2,1,2)
plot(mrr_vel_2km, mrr_heights_2km/1000, 'b-', 'LineWidth', 3, 'DisplayName', 'MRR VEL (参考)')
hold on
plot(abs(ka_vel_2km), range_ka_2km/1000, 'g--', 'LineWidth', 2, 'DisplayName', 'Ka (校正前)')
plot(abs(ka_vel_2km), range_ka_corrected(radar_mask)/1000, 'r-', 'LineWidth', 2, 'DisplayName', 'Ka (校正后)')
plot(abs(w_vel_2km), range_ka_2km/1000, 'c--', 'LineWidth', 2, 'DisplayName', 'W (校正前)')
plot(abs(w_vel_2km), range_w_corrected(radar_mask)/1000, 'm-', 'LineWidth', 2, 'DisplayName', 'W (校正后)')
ylabel('高度 [km]')
xlabel('速度 [m/s]')
title('高度校正效果对比 - 速度剖面')
legend('Location', 'best')
grid on
ylim([0 2])
xlim([0 8])

sgtitle('Ka和W波段高度校正效果对比', 'FontSize', 14, 'FontWeight', 'bold')

fprintf('\n=== 高度校正分析完成 ===\n');
fprintf('建议的高度校正:\n');
fprintf('  Ka波段: %+.0f m\n', avg_offset_ka);
fprintf('  W波段: %+.0f m\n', avg_offset_w);
fprintf('  校正后的高度范围:\n');
fprintf('    Ka: %.0f - %.0f m\n', min(range_ka_corrected), max(range_ka_corrected));
fprintf('    W: %.0f - %.0f m\n', min(range_w_corrected), max(range_w_corrected));
