clear
close all
clc

%% MRR数据可视化脚本 - 2025年1月8日数据
% 此脚本用于读取和可视化MRR (Micro Rain Radar) 数据

% 设置数据目录和日期
data_dir = '20250108';
date_str = '20250108';

% 获取所有nc文件
nc_files = dir(fullfile(data_dir, '*.nc'));
fprintf('找到 %d 个NetCDF文件\n', length(nc_files));

% 初始化数据存储
all_time = [];
all_Za = [];
all_Z = [];
all_RR = [];
range_data = [];

%% 读取所有小时数据
for i = 1:length(nc_files)
    filename = fullfile(data_dir, nc_files(i).name);
    fprintf('正在处理: %s\n', nc_files(i).name);

    try
        % 读取基本信息
        if i == 1
            % 只在第一个文件读取位置和高度信息
            lat = ncread(filename, 'latitude');
            lon = ncread(filename, 'longitude');
            range_data = ncread(filename, 'range');
        end

        % 读取时间序列数据
        Za_temp = ncread(filename, 'Za');
        Z_temp = ncread(filename, 'Z');
        RR_temp = ncread(filename, 'RR');
        time_temp = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');

        % 合并数据
        all_time = [all_time; time_temp];
        if isempty(all_Za)
            all_Za = Za_temp;
            all_Z = Z_temp;
            all_RR = RR_temp;
        else
            all_Za = [all_Za, Za_temp];
            all_Z = [all_Z, Z_temp];
            all_RR = [all_RR, RR_temp];
        end

    catch ME
        fprintf('读取文件 %s 时出错: %s\n', nc_files(i).name, ME.message);
    end
end

fprintf('数据读取完成。时间点数: %d, 高度层数: %d\n', length(all_time), length(range_data));

%% 创建综合可视化图表

% 图1: 反射率因子时间-高度图
figure('Position', [100, 100, 1200, 800]);

subplot(2,2,1)
pcolor(all_time, range_data/1000, all_Za)
shading flat
clb1 = colorbar;
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
ylabel(clb1, '反射率因子 [dBZ]')
title('反射率因子 Za - 2025年1月8日')
set(gca, 'FontSize', 12)

% 图2: 等效反射率因子
subplot(2,2,2)
pcolor(all_time, range_data/1000, all_Z)
shading flat
clb2 = colorbar;
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
ylabel(clb2, '等效反射率因子 [dBZ]')
title('等效反射率因子 Z - 2025年1月8日')
set(gca, 'FontSize', 12)

% 图3: 降雨率
subplot(2,2,3)
pcolor(all_time, range_data/1000, log10(all_RR + 0.01))  % 对数尺度显示降雨率
shading flat
clb3 = colorbar;
ylim([0 4])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
ylabel(clb3, 'log10(降雨率) [mm/h]')
title('降雨率 RR - 2025年1月8日')
set(gca, 'FontSize', 12)

% 图4: 地面降雨率时间序列
subplot(2,2,4)
ground_level_idx = 1;  % 假设第一层是地面层
ground_RR = squeeze(all_RR(ground_level_idx, :));
plot(all_time, ground_RR, 'b-', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('降雨率 [mm/h]')
xlabel('时间 [UTC]')
title('地面降雨率时间序列')
grid on
set(gca, 'FontSize', 12)

sgtitle('MRR数据分析 - 2025年1月8日', 'FontSize', 16, 'FontWeight', 'bold')

%% 保存图片
print('MRR_analysis_20250108', '-dpng', '-r300')
fprintf('图片已保存为: MRR_analysis_20250108.png\n');
