clear
close all
clc

% 读取nc文件地址 
filename = '20240327_120000.nc'

% 读取nc文件信息
infonc = ncinfo(filename);
lat = ncread(filename , 'latitude');
lon = ncread(filename , 'longitude');
Za = ncread(filename , 'Za');
Z = ncread(filename , 'Z');
RR = ncread(filename , 'RR');
range = ncread(filename , 'range');
time = ncread(filename , 'time'  )/3600/24 + datenum('1970-01-01','yyyy-mm-dd') ;

% 画图
figure
pcolor(time, range/1000, Za)
shading flat
clb = colorbar;
ylim([0 4 ])
caxis([-20 20])
datetick('x','HH:MM')
ylabel('Height [km]')
xlabel('Time [UTC]')
ylabel(clb , 'Reflectivity factor [dBZ]')
set(gca,'FontSize',15)
