%% 快速绘图脚本 - 2025年1月8日MRR数据
% 用于快速查看单个文件的数据结构和内容

clear
close all
clc

%% 选择一个文件进行快速分析
filename = '20250108/20250108_120000.nc';  % 选择中午12点的数据

% 检查文件是否存在
if ~exist(filename, 'file')
    fprintf('文件不存在: %s\n', filename);
    fprintf('可用文件:\n');
    files = dir('20250108/*.nc');
    for i = 1:length(files)
        fprintf('  %s\n', files(i).name);
    end
    return
end

%% 读取文件信息
fprintf('正在分析文件: %s\n', filename);
info = ncinfo(filename);

% 显示文件结构
fprintf('\n=== 文件信息 ===\n');
fprintf('维度:\n');
for i = 1:length(info.Dimensions)
    fprintf('  %s: %d\n', info.Dimensions(i).Name, info.Dimensions(i).Length);
end

fprintf('\n变量:\n');
for i = 1:length(info.Variables)
    var = info.Variables(i);
    fprintf('  %s: %s\n', var.Name, mat2str(var.Size));
end

%% 读取数据
try
    lat = ncread(filename, 'latitude');
    lon = ncread(filename, 'longitude');
    Za = ncread(filename, 'Za');
    Z = ncread(filename, 'Z');
    RR = ncread(filename, 'RR');
    range = ncread(filename, 'range');
    time = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    fprintf('\n=== 数据概览 ===\n');
    fprintf('位置: 纬度 %.4f°, 经度 %.4f°\n', lat, lon);
    fprintf('时间范围: %s 到 %s\n', datestr(min(time)), datestr(max(time)));
    fprintf('高度范围: %.1f 到 %.1f m\n', min(range), max(range));
    fprintf('Za范围: %.1f 到 %.1f dBZ\n', min(Za(:)), max(Za(:)));
    fprintf('Z范围: %.1f 到 %.1f dBZ\n', min(Z(:)), max(Z(:)));
    fprintf('RR范围: %.4f 到 %.4f mm/h\n', min(RR(:)), max(RR(:)));
    
catch ME
    fprintf('读取数据时出错: %s\n', ME.message);
    return
end

%% 创建快速可视化
figure('Position', [100, 100, 1200, 900]);

% 子图1: 反射率因子
subplot(2,3,1)
pcolor(time, range/1000, Za)
shading flat
colorbar
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('反射率因子 Za')

% 子图2: 等效反射率因子
subplot(2,3,2)
pcolor(time, range/1000, Z)
shading flat
colorbar
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('等效反射率因子 Z')

% 子图3: 降雨率
subplot(2,3,3)
pcolor(time, range/1000, log10(RR + 0.01))
shading flat
colorbar
ylim([0 4])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('log10(降雨率)')

% 子图4: 垂直剖面（平均）
subplot(2,3,4)
mean_Za = nanmean(Za, 2);
mean_Z = nanmean(Z, 2);
plot(mean_Za, range/1000, 'b-', 'LineWidth', 2)
hold on
plot(mean_Z, range/1000, 'r--', 'LineWidth', 2)
ylabel('高度 [km]')
xlabel('平均反射率 [dBZ]')
title('平均垂直剖面')
legend('Za', 'Z')
grid on
ylim([0 4])

% 子图5: 时间序列（地面）
subplot(2,3,5)
ground_Za = Za(1, :);
ground_Z = Z(1, :);
plot(time, ground_Za, 'b-', 'LineWidth', 2)
hold on
plot(time, ground_Z, 'r--', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('反射率 [dBZ]')
xlabel('时间 [UTC]')
title('地面反射率时间序列')
legend('Za', 'Z')
grid on

% 子图6: 降雨率时间序列
subplot(2,3,6)
ground_RR = RR(1, :);
plot(time, ground_RR, 'g-', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('降雨率 [mm/h]')
xlabel('时间 [UTC]')
title('地面降雨率')
grid on

sgtitle(sprintf('MRR快速分析 - %s', filename), 'FontSize', 14, 'FontWeight', 'bold')

%% 保存图片
print('MRR_quick_analysis_20250108', '-dpng', '-r300')
fprintf('\n图片已保存为: MRR_quick_analysis_20250108.png\n');

%% 数据质量检查
fprintf('\n=== 数据质量检查 ===\n');
fprintf('Za中NaN值比例: %.2f%%\n', sum(isnan(Za(:)))/numel(Za)*100);
fprintf('Z中NaN值比例: %.2f%%\n', sum(isnan(Z(:)))/numel(Z)*100);
fprintf('RR中NaN值比例: %.2f%%\n', sum(isnan(RR(:)))/numel(RR)*100);

% 检查异常值
fprintf('Za异常值 (>50 dBZ): %d\n', sum(Za(:) > 50));
fprintf('Za异常值 (<-50 dBZ): %d\n', sum(Za(:) < -50));
fprintf('RR异常值 (>100 mm/h): %d\n', sum(RR(:) > 100));

fprintf('\n快速分析完成！\n');
