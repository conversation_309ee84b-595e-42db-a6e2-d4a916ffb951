%% MRR高级数据可视化脚本 - 2025年1月8日
% 此脚本提供更多样化的MRR数据可视化选项

clear
close all
clc

%% 设置参数
data_dir = '20250108';
date_str = '20250108';

% 选择要分析的时间段（小时）
start_hour = 0;   % 开始小时
end_hour = 23;    % 结束小时

% 创建文件列表
file_list = {};
for hour = start_hour:end_hour
    filename = sprintf('%s_%02d0000.nc', date_str, hour);
    file_path = fullfile(data_dir, filename);
    if exist(file_path, 'file')
        file_list{end+1} = file_path;
    end
end

fprintf('将处理 %d 个文件\n', length(file_list));

%% 读取数据
all_data = struct();
for i = 1:length(file_list)
    filename = file_list{i};
    fprintf('读取文件: %s\n', filename);
    
    try
        if i == 1
            % 读取坐标信息
            all_data.lat = ncread(filename, 'latitude');
            all_data.lon = ncread(filename, 'longitude');
            all_data.range = ncread(filename, 'range');
        end
        
        % 读取数据
        Za_temp = ncread(filename, 'Za');
        Z_temp = ncread(filename, 'Z');
        RR_temp = ncread(filename, 'RR');
        time_temp = ncread(filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        % 存储数据
        if i == 1
            all_data.time = time_temp;
            all_data.Za = Za_temp;
            all_data.Z = Z_temp;
            all_data.RR = RR_temp;
        else
            all_data.time = [all_data.time; time_temp];
            all_data.Za = [all_data.Za, Za_temp];
            all_data.Z = [all_data.Z, Z_temp];
            all_data.RR = [all_data.RR, RR_temp];
        end
        
    catch ME
        fprintf('读取文件出错: %s\n', ME.message);
    end
end

%% 创建多种可视化图表

% 图表1: 详细的反射率分析
figure('Position', [50, 50, 1400, 1000]);

% 子图1: 反射率因子热图
subplot(3,3,1)
pcolor(all_data.time, all_data.range/1000, all_data.Za)
shading flat
colorbar
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('反射率因子 Za')
set(gca, 'FontSize', 10)

% 子图2: 等效反射率因子热图
subplot(3,3,2)
pcolor(all_data.time, all_data.range/1000, all_data.Z)
shading flat
colorbar
ylim([0 4])
caxis([-20 20])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('等效反射率因子 Z')
set(gca, 'FontSize', 10)

% 子图3: 降雨率热图
subplot(3,3,3)
RR_log = log10(all_data.RR + 0.01);  % 避免log(0)
pcolor(all_data.time, all_data.range/1000, RR_log)
shading flat
colorbar
ylim([0 4])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('降雨率 log10(RR)')
set(gca, 'FontSize', 10)

% 子图4: 垂直剖面（选择中间时刻）
subplot(3,3,4)
mid_time_idx = round(length(all_data.time)/2);
plot(all_data.Za(:, mid_time_idx), all_data.range/1000, 'b-', 'LineWidth', 2)
hold on
plot(all_data.Z(:, mid_time_idx), all_data.range/1000, 'r--', 'LineWidth', 2)
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title(sprintf('垂直剖面 (时间: %s)', datestr(all_data.time(mid_time_idx), 'HH:MM')))
legend('Za', 'Z', 'Location', 'best')
grid on
ylim([0 4])
set(gca, 'FontSize', 10)

% 子图5: 时间序列（选择特定高度）
subplot(3,3,5)
height_idx = find(all_data.range/1000 >= 1, 1);  % 1km高度
plot(all_data.time, all_data.Za(height_idx, :), 'b-', 'LineWidth', 2)
hold on
plot(all_data.time, all_data.Z(height_idx, :), 'r--', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('反射率 [dBZ]')
xlabel('时间 [UTC]')
title(sprintf('时间序列 (高度: %.1f km)', all_data.range(height_idx)/1000))
legend('Za', 'Z', 'Location', 'best')
grid on
set(gca, 'FontSize', 10)

% 子图6: 地面降雨率
subplot(3,3,6)
ground_RR = all_data.RR(1, :);  % 地面层
plot(all_data.time, ground_RR, 'g-', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('降雨率 [mm/h]')
xlabel('时间 [UTC]')
title('地面降雨率')
grid on
set(gca, 'FontSize', 10)

% 子图7: 反射率差值 (Za - Z)
subplot(3,3,7)
diff_ZaZ = all_data.Za - all_data.Z;
pcolor(all_data.time, all_data.range/1000, diff_ZaZ)
shading flat
colorbar
ylim([0 4])
caxis([-5 5])
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('反射率差值 (Za - Z)')
set(gca, 'FontSize', 10)

% 子图8: 最大反射率高度
subplot(3,3,8)
[max_Za, max_idx] = max(all_data.Za, [], 1);
max_height = all_data.range(max_idx) / 1000;
plot(all_data.time, max_height, 'ko-', 'LineWidth', 1.5, 'MarkerSize', 4)
datetick('x', 'HH:MM')
ylabel('高度 [km]')
xlabel('时间 [UTC]')
title('最大反射率高度')
grid on
ylim([0 4])
set(gca, 'FontSize', 10)

% 子图9: 统计信息
subplot(3,3,9)
mean_Za = nanmean(all_data.Za, 2);
std_Za = nanstd(all_data.Za, 0, 2);
plot(mean_Za, all_data.range/1000, 'b-', 'LineWidth', 2)
hold on
plot(mean_Za + std_Za, all_data.range/1000, 'b--', 'LineWidth', 1)
plot(mean_Za - std_Za, all_data.range/1000, 'b--', 'LineWidth', 1)
ylabel('高度 [km]')
xlabel('反射率 [dBZ]')
title('Za统计 (均值±标准差)')
grid on
ylim([0 4])
set(gca, 'FontSize', 10)

sgtitle('MRR数据综合分析 - 2025年1月8日', 'FontSize', 16, 'FontWeight', 'bold')

%% 保存图片
print('MRR_advanced_analysis_20250108', '-dpng', '-r300')
fprintf('高级分析图已保存为: MRR_advanced_analysis_20250108.png\n');

%% 创建动画（可选）
create_animation = input('是否创建动画? (1=是, 0=否): ');
if create_animation == 1
    fprintf('正在创建动画...\n');
    
    figure('Position', [100, 100, 800, 600]);
    
    % 创建视频写入器
    v = VideoWriter('MRR_animation_20250108.mp4', 'MPEG-4');
    v.FrameRate = 2;  % 2帧/秒
    open(v);
    
    for i = 1:10:length(all_data.time)  % 每10个时间点一帧
        clf
        
        % 绘制当前时刻的垂直剖面
        subplot(1,2,1)
        plot(all_data.Za(:, i), all_data.range/1000, 'b-', 'LineWidth', 2)
        hold on
        plot(all_data.Z(:, i), all_data.range/1000, 'r--', 'LineWidth', 2)
        ylabel('高度 [km]')
        xlabel('反射率 [dBZ]')
        title(sprintf('垂直剖面\n时间: %s', datestr(all_data.time(i), 'yyyy-mm-dd HH:MM')))
        legend('Za', 'Z', 'Location', 'best')
        grid on
        ylim([0 4])
        xlim([-20 20])
        
        % 绘制时间-高度图（到当前时刻）
        subplot(1,2,2)
        pcolor(all_data.time(1:i), all_data.range/1000, all_data.Za(:, 1:i))
        shading flat
        colorbar
        ylim([0 4])
        caxis([-20 20])
        datetick('x', 'HH:MM')
        ylabel('高度 [km]')
        xlabel('时间 [UTC]')
        title('反射率因子 Za')
        
        sgtitle(sprintf('MRR数据动画 - %s', datestr(all_data.time(i), 'yyyy-mm-dd HH:MM')), ...
                'FontSize', 14, 'FontWeight', 'bold')
        
        % 捕获帧
        frame = getframe(gcf);
        writeVideo(v, frame);
    end
    
    close(v);
    fprintf('动画已保存为: MRR_animation_20250108.mp4\n');
end

fprintf('所有可视化完成！\n');
