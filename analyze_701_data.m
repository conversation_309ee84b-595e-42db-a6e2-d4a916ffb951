clear
close all
clc

%% Analysis script for 7:01 data comparison
% Compare MRR data at 7:01 with Ka and W band data from Shandong dataset

fprintf('=== MRR Data Analysis at 7:01 ===\n');

%% 1. Load MRR data - check multiple files to find 7:01
% MRR files seem to be offset, so check 06:00 and 07:00 files
mrr_files_to_check = {'20250108/20250108_060000.nc', '20250108/20250108_070000.nc'};
mrr_filename = '';

for i = 1:length(mrr_files_to_check)
    if exist(mrr_files_to_check{i}, 'file')
        % Check time range in this file
        try
            temp_time = ncread(mrr_files_to_check{i}, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
            target_time = datenum('2025-01-08 07:01:00', 'yyyy-mm-dd HH:MM:SS');

            if any(abs(temp_time - target_time) < 1/24)  % Within 1 hour
                mrr_filename = mrr_files_to_check{i};
                fprintf('Found 7:01 data in file: %s\n', mrr_filename);
                break;
            end
        catch
            continue;
        end
    end
end

if isempty(mrr_filename)
    fprintf('Could not find MRR file containing 7:01 data\n');
    mrr_filename = '20250108/20250108_070000.nc';  % Use as fallback
end

if ~exist(mrr_filename, 'file')
    fprintf('MRR file not found: %s\n', mrr_filename);
    return
end

% Read MRR data
fprintf('Loading MRR data from: %s\n', mrr_filename);
try
    mrr_lat = ncread(mrr_filename, 'latitude');
    mrr_lon = ncread(mrr_filename, 'longitude');
    mrr_Za = ncread(mrr_filename, 'Za');
    mrr_Z = ncread(mrr_filename, 'Z');
    mrr_RR = ncread(mrr_filename, 'RR');
    mrr_range = ncread(mrr_filename, 'range');
    mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
    
    fprintf('MRR data loaded successfully\n');
    fprintf('MRR Location: Lat %.4f°, Lon %.4f°\n', mrr_lat, mrr_lon);
    fprintf('MRR Height range: %.1f to %.1f m\n', min(mrr_range), max(mrr_range));
    fprintf('MRR Time range: %s to %s\n', datestr(min(mrr_time)), datestr(max(mrr_time)));
    
catch ME
    fprintf('Error loading MRR data: %s\n', ME.message);
    return
end

%% 2. Find data at 7:01
% Convert 7:01 to datenum for comparison
target_time = datenum('2025-01-08 07:01:00', 'yyyy-mm-dd HH:MM:SS');
fprintf('\nTarget time: %s\n', datestr(target_time));

% Find closest time index
[~, time_idx] = min(abs(mrr_time - target_time));
actual_time = mrr_time(time_idx);
fprintf('Closest MRR time: %s\n', datestr(actual_time));
fprintf('Time difference: %.2f minutes\n', (actual_time - target_time) * 24 * 60);

%% 3. Analyze MRR data at 7:01
mrr_Za_701 = mrr_Za(:, time_idx);
mrr_Z_701 = mrr_Z(:, time_idx);
mrr_RR_701 = mrr_RR(:, time_idx);

% Find heights with valid data (not NaN and above threshold)
valid_Za_idx = ~isnan(mrr_Za_701) & mrr_Za_701 > -30;  % Above noise threshold
valid_Z_idx = ~isnan(mrr_Z_701) & mrr_Z_701 > -30;
valid_RR_idx = ~isnan(mrr_RR_701) & mrr_RR_701 > 0.01;

mrr_heights_with_data_Za = mrr_range(valid_Za_idx);
mrr_heights_with_data_Z = mrr_range(valid_Z_idx);
mrr_heights_with_data_RR = mrr_range(valid_RR_idx);

fprintf('\n=== MRR Data at 7:01 ===\n');
fprintf('Heights with Za data (>-30 dBZ): %d levels\n', length(mrr_heights_with_data_Za));
if ~isempty(mrr_heights_with_data_Za)
    fprintf('  Range: %.1f to %.1f m\n', min(mrr_heights_with_data_Za), max(mrr_heights_with_data_Za));
    fprintf('  Heights: ');
    for i = 1:min(10, length(mrr_heights_with_data_Za))
        fprintf('%.1f ', mrr_heights_with_data_Za(i));
    end
    if length(mrr_heights_with_data_Za) > 10
        fprintf('... (and %d more)', length(mrr_heights_with_data_Za) - 10);
    end
    fprintf(' m\n');
end

fprintf('Heights with Z data (>-30 dBZ): %d levels\n', length(mrr_heights_with_data_Z));
fprintf('Heights with RR data (>0.01 mm/h): %d levels\n', length(mrr_heights_with_data_RR));

%% 4. Load Shandong Ka and W band data
% Try multiple possible locations and filenames
possible_shandong_files = {
    'D:\dataset\shandong 2025-01-08_reprocessed.mat',
    'D:\dataset\shandong_2025-01-08_reprocessed.mat',
    'D:\dataset\shandong\2025-01-08_reprocessed.mat',
    'shandong 2025-01-08_reprocessed.mat',
    'shandong_2025-01-08_reprocessed.mat'
};

shandong_filename = '';
for i = 1:length(possible_shandong_files)
    if exist(possible_shandong_files{i}, 'file')
        shandong_filename = possible_shandong_files{i};
        break;
    end
end

if isempty(shandong_filename)
    fprintf('\nShandong file not found in any of these locations:\n');
    for i = 1:length(possible_shandong_files)
        fprintf('  %s\n', possible_shandong_files{i});
    end
    fprintf('Please check the file path and name.\n');
    fprintf('Continuing with MRR analysis only...\n');
    shandong_data_available = false;
else
    shandong_data_available = true;
end

if shandong_data_available
    fprintf('\n=== Loading Shandong Ka and W band data ===\n');
    fprintf('Loading from: %s\n', shandong_filename);
    try
        shandong_data = load(shandong_filename);
        fprintf('Shandong data loaded successfully\n');
    
    % Display structure of loaded data
    fprintf('Variables in Shandong dataset:\n');
    field_names = fieldnames(shandong_data);
    for i = 1:length(field_names)
        var_name = field_names{i};
        var_data = shandong_data.(var_name);
        if isnumeric(var_data)
            fprintf('  %s: %s\n', var_name, mat2str(size(var_data)));
        else
            fprintf('  %s: %s\n', var_name, class(var_data));
        end
    end
    
    catch ME
        fprintf('Error loading Shandong data: %s\n', ME.message);
        shandong_data_available = false;
    end
end

%% 5. Find Ka and W band data at 7:01
if shandong_data_available
    % This part depends on the actual structure of the Shandong data
    % We need to identify time and height variables first

    fprintf('\n=== Analyzing Shandong data structure ===\n');

% Common variable names to look for
time_vars = {'time', 'Time', 'datetime', 'timestamp'};
height_vars = {'height', 'Height', 'range', 'Range', 'altitude', 'Altitude'};
ka_vars = {'Ka', 'ka', 'Ka_band', 'ka_band', 'reflectivity_ka', 'Z_ka'};
w_vars = {'W', 'w', 'W_band', 'w_band', 'reflectivity_w', 'Z_w'};

% Find time variable
time_var = '';
for i = 1:length(time_vars)
    if isfield(shandong_data, time_vars{i})
        time_var = time_vars{i};
        break;
    end
end

% Find height variable
height_var = '';
for i = 1:length(height_vars)
    if isfield(shandong_data, height_vars{i})
        height_var = height_vars{i};
        break;
    end
end

% Find Ka band variable
ka_var = '';
for i = 1:length(ka_vars)
    if isfield(shandong_data, ka_vars{i})
        ka_var = ka_vars{i};
        break;
    end
end

% Find W band variable
w_var = '';
for i = 1:length(w_vars)
    if isfield(shandong_data, w_vars{i})
        w_var = w_vars{i};
        break;
    end
end

fprintf('Identified variables:\n');
fprintf('  Time: %s\n', time_var);
fprintf('  Height: %s\n', height_var);
fprintf('  Ka band: %s\n', ka_var);
fprintf('  W band: %s\n', w_var);

%% 6. Extract and compare data if variables are found
if ~isempty(time_var) && ~isempty(height_var)
    try
        shandong_time = shandong_data.(time_var);
        shandong_height = shandong_data.(height_var);
        
        fprintf('\nShandong time range: ');
        if isnumeric(shandong_time)
            if length(shandong_time) > 1
                fprintf('%s to %s\n', datestr(min(shandong_time)), datestr(max(shandong_time)));
            else
                fprintf('%s\n', datestr(shandong_time));
            end
        else
            fprintf('Non-numeric time format\n');
        end
        
        fprintf('Shandong height range: %.1f to %.1f m\n', min(shandong_height(:)), max(shandong_height(:)));
        
        % Find closest time to 7:01
        if isnumeric(shandong_time)
            [~, shandong_time_idx] = min(abs(shandong_time - target_time));
            shandong_actual_time = shandong_time(shandong_time_idx);
            fprintf('Closest Shandong time: %s\n', datestr(shandong_actual_time));
            fprintf('Time difference: %.2f minutes\n', (shandong_actual_time - target_time) * 24 * 60);
            
            % Extract Ka and W band data at 7:01
            if ~isempty(ka_var)
                ka_data = shandong_data.(ka_var);
                if ndims(ka_data) == 2
                    ka_701 = ka_data(:, shandong_time_idx);
                    valid_ka_idx = ~isnan(ka_701) & ka_701 > -30;
                    ka_heights_with_data = shandong_height(valid_ka_idx);
                    
                    fprintf('\n=== Ka band data at 7:01 ===\n');
                    fprintf('Heights with Ka data (>-30 dBZ): %d levels\n', length(ka_heights_with_data));
                    if ~isempty(ka_heights_with_data)
                        fprintf('  Range: %.1f to %.1f m\n', min(ka_heights_with_data), max(ka_heights_with_data));
                    end
                end
            end
            
            if ~isempty(w_var)
                w_data = shandong_data.(w_var);
                if ndims(w_data) == 2
                    w_701 = w_data(:, shandong_time_idx);
                    valid_w_idx = ~isnan(w_701) & w_701 > -30;
                    w_heights_with_data = shandong_height(valid_w_idx);
                    
                    fprintf('\n=== W band data at 7:01 ===\n');
                    fprintf('Heights with W data (>-30 dBZ): %d levels\n', length(w_heights_with_data));
                    if ~isempty(w_heights_with_data)
                        fprintf('  Range: %.1f to %.1f m\n', min(w_heights_with_data), max(w_heights_with_data));
                    end
                end
            end
        end
        
    catch ME
        fprintf('Error analyzing Shandong data: %s\n', ME.message);
    end
    else
        fprintf('\nCould not identify time and/or height variables in Shandong data.\n');
        fprintf('Please check the variable names in the dataset.\n');
    end
else
    fprintf('\n=== Shandong data not available - continuing with MRR analysis only ===\n');
end

%% 7. Create visualization
figure('Position', [100, 100, 1200, 800]);

% Plot MRR data
subplot(2,2,1)
plot(mrr_Za_701, mrr_range/1000, 'b-', 'LineWidth', 2)
hold on
plot(mrr_Z_701, mrr_range/1000, 'r--', 'LineWidth', 2)
ylabel('Height [km]')
xlabel('Reflectivity [dBZ]')
title('MRR Data at 7:01')
legend('Za', 'Z', 'Location', 'best')
grid on
ylim([0 4])
xlim([-40 20])

% Plot data availability
subplot(2,2,2)
plot(double(valid_Za_idx), mrr_range/1000, 'b-', 'LineWidth', 2)
hold on
plot(double(valid_Z_idx), mrr_range/1000, 'r--', 'LineWidth', 2)
ylabel('Height [km]')
xlabel('Data Available (1=Yes, 0=No)')
title('MRR Data Availability at 7:01')
legend('Za', 'Z', 'Location', 'best')
grid on
ylim([0 4])

% If Ka and W band data are available, plot them
if exist('ka_701', 'var')
    subplot(2,2,3)
    plot(ka_701, shandong_height/1000, 'g-', 'LineWidth', 2)
    ylabel('Height [km]')
    xlabel('Reflectivity [dBZ]')
    title('Ka Band Data at 7:01')
    grid on
    ylim([0 4])
    xlim([-40 20])
end

if exist('w_701', 'var')
    subplot(2,2,4)
    plot(w_701, shandong_height/1000, 'm-', 'LineWidth', 2)
    ylabel('Height [km]')
    xlabel('Reflectivity [dBZ]')
    title('W Band Data at 7:01')
    grid on
    ylim([0 4])
    xlim([-40 20])
end

sgtitle('Radar Data Comparison at 7:01 - January 8, 2025', 'FontSize', 14, 'FontWeight', 'bold')

% Save figure
print('radar_comparison_701', '-dpng', '-r300')
fprintf('\nComparison plot saved as: radar_comparison_701.png\n');

fprintf('\n=== Analysis Complete ===\n');
