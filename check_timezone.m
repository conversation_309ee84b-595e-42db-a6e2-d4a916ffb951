clear
close all
clc

%% 检查MRR和云雷达数据的时区设置
% MRR可能是UTC，云雷达可能是BJT (UTC+8)

fprintf('=== 检查MRR和云雷达数据时区 ===\n\n');

%% 1. 检查MRR数据时区
fprintf('1. 检查MRR数据时区\n');

% 检查几个不同时间的MRR文件
mrr_files_to_check = {
    '20250108/20250108_000000.nc',  % 00:00文件
    '20250108/20250108_120000.nc',  % 12:00文件
    '20250108/20250108_200000.nc'   % 20:00文件
};

for i = 1:length(mrr_files_to_check)
    mrr_file = mrr_files_to_check{i};
    if exist(mrr_file, 'file')
        fprintf('\n检查文件: %s\n', mrr_file);
        
        try
            % 读取时间和其他属性
            mrr_time = ncread(mrr_file, 'time');
            
            % 检查时间属性
            time_info = ncinfo(mrr_file, 'time');
            fprintf('  时间变量属性:\n');
            for j = 1:length(time_info.Attributes)
                attr = time_info.Attributes(j);
                fprintf('    %s: %s\n', attr.Name, attr.Value);
            end
            
            % 转换时间
            mrr_time_datenum = mrr_time / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
            
            fprintf('  原始时间范围: %.0f - %.0f 秒\n', min(mrr_time), max(mrr_time));
            fprintf('  转换后时间范围:\n');
            fprintf('    开始: %s\n', datestr(min(mrr_time_datenum)));
            fprintf('    结束: %s\n', datestr(max(mrr_time_datenum)));
            
            % 检查文件名时间与数据时间的关系
            file_hour = str2double(mrr_file(end-9:end-8));
            data_hour_start = hour(datetime(datevec(min(mrr_time_datenum))));
            data_hour_end = hour(datetime(datevec(max(mrr_time_datenum))));
            
            fprintf('  文件名显示小时: %02d\n', file_hour);
            fprintf('  数据实际小时范围: %02d - %02d\n', data_hour_start, data_hour_end);
            
            % 分析时区可能性
            if data_hour_start == file_hour
                fprintf('  → 可能是UTC时间 (文件名与数据时间一致)\n');
            elseif mod(data_hour_start + 8, 24) == file_hour
                fprintf('  → 可能是BJT时间 (数据时间+8小时=文件名时间)\n');
            else
                fprintf('  → 时区关系不明确\n');
            end
            
        catch ME
            fprintf('  错误: %s\n', ME.message);
        end
    else
        fprintf('文件不存在: %s\n', mrr_file);
    end
end

%% 2. 检查云雷达数据时区
fprintf('\n\n2. 检查云雷达数据时区\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
if exist(shandong_filename, 'file')
    fprintf('检查文件: %s\n', shandong_filename);
    
    try
        shandong_data = load(shandong_filename);
        
        if isfield(shandong_data, 'obs_time')
            obs_time = shandong_data.obs_time;
            
            fprintf('  obs_time数据类型: %s\n', class(obs_time));
            fprintf('  obs_time维度: %s\n', mat2str(size(obs_time)));
            fprintf('  obs_time范围: %.6f - %.6f\n', min(obs_time), max(obs_time));
            
            % 尝试不同的时间转换方法
            fprintf('\n  时间转换尝试:\n');
            
            % 方法1: 假设是datenum格式
            if min(obs_time) > 700000  % 典型的datenum值
                fprintf('    方法1 (datenum): %s - %s\n', ...
                    datestr(min(obs_time)), datestr(max(obs_time)));
                
                % 检查是否为BJT时间
                start_hour = hour(datetime(datevec(min(obs_time))));
                end_hour = hour(datetime(datevec(max(obs_time))));
                fprintf('    小时范围: %02d - %02d\n', start_hour, end_hour);
                
                % 如果是0-23小时，可能是BJT；如果是16-15（次日），可能是UTC
                if start_hour == 0 && end_hour == 23
                    fprintf('    → 可能是BJT时间 (0-23小时覆盖)\n');
                elseif start_hour == 16 && end_hour == 15
                    fprintf('    → 可能是UTC时间 (16-15小时，跨日)\n');
                else
                    fprintf('    → 时区不明确\n');
                end
            end
            
            % 方法2: 假设是Unix时间戳
            if min(obs_time) > 1000000000  % Unix时间戳
                unix_time = obs_time;
                matlab_time = unix_time / 86400 + datenum('1970-01-01');
                fprintf('    方法2 (Unix时间戳): %s - %s\n', ...
                    datestr(min(matlab_time)), datestr(max(matlab_time)));
            end
            
            % 方法3: 假设是相对时间
            if min(obs_time) < 100000
                fprintf('    方法3: 可能是相对时间或其他格式\n');
            end
            
        else
            fprintf('  未找到obs_time字段\n');
        end
        
        % 检查其他可能的时间字段
        fprintf('\n  所有字段:\n');
        field_names = fieldnames(shandong_data);
        for j = 1:length(field_names)
            field_name = field_names{j};
            if contains(lower(field_name), 'time') || contains(lower(field_name), 'date')
                fprintf('    时间相关字段: %s\n', field_name);
            end
        end
        
    catch ME
        fprintf('  错误: %s\n', ME.message);
    end
else
    fprintf('文件不存在: %s\n', shandong_filename);
end

%% 3. 时区对比分析
fprintf('\n\n3. 时区对比分析\n');

% 如果两个数据源都可用，进行时区对比
if exist('mrr_time_datenum', 'var') && exist('obs_time', 'var')
    fprintf('进行时区对比分析...\n');
    
    % 选择一个具体时间点进行对比
    target_mrr_time = mrr_time_datenum(1);  % 第一个时间点
    target_obs_time = obs_time(1);
    
    fprintf('  MRR第一个时间点: %s\n', datestr(target_mrr_time));
    fprintf('  云雷达第一个时间点: %s\n', datestr(target_obs_time));
    
    % 计算时间差
    time_diff_hours = (target_obs_time - target_mrr_time) * 24;
    fprintf('  时间差: %.1f 小时\n', time_diff_hours);
    
    if abs(time_diff_hours - 8) < 1
        fprintf('  → 云雷达可能比MRR快8小时 (BJT vs UTC)\n');
    elseif abs(time_diff_hours + 8) < 1
        fprintf('  → MRR可能比云雷达快8小时 (UTC vs BJT)\n');
    elseif abs(time_diff_hours) < 1
        fprintf('  → 两者时区可能相同\n');
    else
        fprintf('  → 时区关系不明确\n');
    end
end

%% 4. 建议的时区处理方案
fprintf('\n\n4. 时区处理建议\n');
fprintf('根据分析结果:\n');
fprintf('  - 如果MRR是UTC时间，云雷达是BJT时间\n');
fprintf('  - 需要将其中一个转换为统一时区\n');
fprintf('  - BJT = UTC + 8小时\n');
fprintf('  - UTC = BJT - 8小时\n');
fprintf('\n转换方案:\n');
fprintf('  方案1: 将云雷达时间转为UTC (减8小时)\n');
fprintf('  方案2: 将MRR时间转为BJT (加8小时)\n');
fprintf('\n建议使用UTC作为统一时区进行科学分析\n');

fprintf('\n=== 时区检查完成 ===\n');
