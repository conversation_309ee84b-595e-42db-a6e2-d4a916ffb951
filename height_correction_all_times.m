clear
close all
clc

%% 基于重合时间段所有时刻的高度校正分析
% 对重合时间段内的每个时刻进行校正分析，然后取平均值

fprintf('=== 基于所有重合时刻的高度校正分析 ===\n');
fprintf('方法: 对重合时间段内每个时刻进行校正，取平均值\n\n');

%% 1. 加载云雷达数据
fprintf('1. 加载云雷达数据\n');

shandong_filename = 'D:\dataset\shandong\2025-01-08_reprocessed.mat';
try
    shandong_data = load(shandong_filename);
    obs_time_bjt = shandong_data.obs_time;
    obs_time_utc = obs_time_bjt - 8/24;  % BJT转UTC
    
    range_ka_original = shandong_data.range_ka;
    if max(range_ka_original) < 100
        range_ka_original = range_ka_original * 1000;
    end
    
    ka_ze = shandong_data.ze.ka;
    w_ze = shandong_data.ze.w;
    ka_vel = shandong_data.vel.ka;
    w_vel = shandong_data.vel.w;
    
    fprintf('   云雷达数据加载完成\n');
    
catch ME
    fprintf('   错误: %s\n', ME.message);
    return
end

%% 2. 加载MRR重合时间段数据
fprintf('\n2. 加载MRR重合时间段数据\n');

radar_start_utc = min(obs_time_utc);
radar_end_utc = max(obs_time_utc);

% 收集所有重合的MRR数据
all_mrr_time = [];
all_mrr_Za = [];
all_mrr_VEL = [];
all_mrr_range = [];

mrr_files = dir('20250108/*.nc');
for i = 1:length(mrr_files)
    mrr_filename = fullfile('20250108', mrr_files(i).name);
    try
        mrr_time = ncread(mrr_filename, 'time') / 3600 / 24 + datenum('1970-01-01', 'yyyy-mm-dd');
        
        if max(mrr_time) >= radar_start_utc && min(mrr_time) <= radar_end_utc
            mrr_Za = ncread(mrr_filename, 'Za');
            mrr_VEL = ncread(mrr_filename, 'VEL');
            mrr_range = ncread(mrr_filename, 'range');
            
            overlap_mask = mrr_time >= radar_start_utc & mrr_time <= radar_end_utc;
            
            if any(overlap_mask)
                if isempty(all_mrr_time)
                    all_mrr_time = mrr_time(overlap_mask);
                    all_mrr_Za = mrr_Za(:, overlap_mask);
                    all_mrr_VEL = mrr_VEL(:, overlap_mask);
                    all_mrr_range = mrr_range;
                else
                    all_mrr_time = [all_mrr_time; mrr_time(overlap_mask)];
                    all_mrr_Za = [all_mrr_Za, mrr_Za(:, overlap_mask)];
                    all_mrr_VEL = [all_mrr_VEL, mrr_VEL(:, overlap_mask)];
                end
            end
        end
    catch
        continue;
    end
end

fprintf('   MRR重合数据: %d个时间点\n', length(all_mrr_time));

%% 3. 设置校正参数
fprintf('\n3. 设置校正分析参数\n');

% 高度限制
height_limit = 2000;  % 2km以下
mrr_2km_mask = all_mrr_range <= height_limit;
radar_2km_mask = range_ka_original <= height_limit;

% 校正搜索范围
height_offsets = -400:10:200;  % 测试-400到+200m，步长10m
common_heights = 300:20:2000;  % 通用高度网格，20m分辨率

fprintf('   高度限制: %dm以下\n', height_limit);
fprintf('   校正搜索范围: %d到%dm，步长%dm\n', min(height_offsets), max(height_offsets), 10);
fprintf('   通用高度网格: %d层，分辨率20m\n', length(common_heights));

%% 4. 对每个时刻进行校正分析
fprintf('\n4. 对所有重合时刻进行校正分析\n');

% 采样时间点（避免计算量过大）
sample_interval = 50;  % 每50个点采样一次
sample_indices = 1:sample_interval:length(all_mrr_time);
n_samples = length(sample_indices);

fprintf('   采样策略: 每%d个时间点采样一次\n', sample_interval);
fprintf('   分析时间点数: %d\n', n_samples);

% 存储每个时刻的最佳校正
ka_offsets_za = zeros(n_samples, 1);
ka_offsets_vel = zeros(n_samples, 1);
w_offsets_za = zeros(n_samples, 1);
w_offsets_vel = zeros(n_samples, 1);

ka_corr_za = zeros(n_samples, 1);
ka_corr_vel = zeros(n_samples, 1);
w_corr_za = zeros(n_samples, 1);
w_corr_vel = zeros(n_samples, 1);

for s = 1:n_samples
    t_idx = sample_indices(s);
    
    if mod(s, 10) == 1
        fprintf('   处理进度: %d/%d (%.1f%%)\n', s, n_samples, s/n_samples*100);
    end
    
    % 提取该时刻的数据
    mrr_time_current = all_mrr_time(t_idx);
    
    % 找到云雷达对应时刻
    [~, radar_idx] = min(abs(obs_time_utc - mrr_time_current));
    
    % 提取垂直剖面
    mrr_za_profile = all_mrr_Za(mrr_2km_mask, t_idx);
    mrr_vel_profile = all_mrr_VEL(mrr_2km_mask, t_idx);
    mrr_heights = all_mrr_range(mrr_2km_mask);
    
    ka_ze_profile = ka_ze(radar_2km_mask, radar_idx);
    w_ze_profile = w_ze(radar_2km_mask, radar_idx);
    ka_vel_profile = ka_vel(radar_2km_mask, radar_idx);
    w_vel_profile = w_vel(radar_2km_mask, radar_idx);
    radar_heights = range_ka_original(radar_2km_mask);
    
    % 检查数据质量
    mrr_valid_count = sum(~isnan(mrr_za_profile) & mrr_za_profile > -20);
    ka_valid_count = sum(~isnan(ka_ze_profile) & ka_ze_profile > -20);
    w_valid_count = sum(~isnan(w_ze_profile) & w_ze_profile > -20);
    
    % 只有足够的有效数据才进行校正
    if mrr_valid_count > 10 && ka_valid_count > 10 && w_valid_count > 10
        
        % 插值MRR到通用网格
        mrr_za_interp = interp1(mrr_heights, mrr_za_profile, common_heights, 'linear', NaN);
        mrr_vel_interp = interp1(mrr_heights, mrr_vel_profile, common_heights, 'linear', NaN);
        
        % 对每个偏移计算相关系数
        best_corr_ka_za = -1;
        best_corr_ka_vel = -1;
        best_corr_w_za = -1;
        best_corr_w_vel = -1;
        
        for offset = height_offsets
            % Ka波段
            adjusted_ka_heights = radar_heights + offset;
            ka_za_interp = interp1(adjusted_ka_heights, ka_ze_profile, common_heights, 'linear', NaN);
            ka_vel_interp = interp1(adjusted_ka_heights, ka_vel_profile, common_heights, 'linear', NaN);
            
            % W波段
            adjusted_w_heights = radar_heights + offset;
            w_za_interp = interp1(adjusted_w_heights, w_ze_profile, common_heights, 'linear', NaN);
            w_vel_interp = interp1(adjusted_w_heights, w_vel_profile, common_heights, 'linear', NaN);
            
            % 计算相关系数
            % Ka反射率
            valid_ka_za = ~isnan(mrr_za_interp) & ~isnan(ka_za_interp);
            if sum(valid_ka_za) > 15
                corr_temp = corrcoef(mrr_za_interp(valid_ka_za), ka_za_interp(valid_ka_za));
                if corr_temp(1,2) > best_corr_ka_za
                    best_corr_ka_za = corr_temp(1,2);
                    ka_offsets_za(s) = offset;
                end
            end
            
            % Ka速度
            valid_ka_vel = ~isnan(mrr_vel_interp) & ~isnan(ka_vel_interp);
            if sum(valid_ka_vel) > 15
                corr_temp = corrcoef(mrr_vel_interp(valid_ka_vel), abs(ka_vel_interp(valid_ka_vel)));
                if corr_temp(1,2) > best_corr_ka_vel
                    best_corr_ka_vel = corr_temp(1,2);
                    ka_offsets_vel(s) = offset;
                end
            end
            
            % W反射率
            valid_w_za = ~isnan(mrr_za_interp) & ~isnan(w_za_interp);
            if sum(valid_w_za) > 15
                corr_temp = corrcoef(mrr_za_interp(valid_w_za), w_za_interp(valid_w_za));
                if corr_temp(1,2) > best_corr_w_za
                    best_corr_w_za = corr_temp(1,2);
                    w_offsets_za(s) = offset;
                end
            end
            
            % W速度
            valid_w_vel = ~isnan(mrr_vel_interp) & ~isnan(w_vel_interp);
            if sum(valid_w_vel) > 15
                corr_temp = corrcoef(mrr_vel_interp(valid_w_vel), abs(w_vel_interp(valid_w_vel)));
                if corr_temp(1,2) > best_corr_w_vel
                    best_corr_w_vel = corr_temp(1,2);
                    w_offsets_vel(s) = offset;
                end
            end
        end
        
        % 保存该时刻的最佳相关系数
        ka_corr_za(s) = best_corr_ka_za;
        ka_corr_vel(s) = best_corr_ka_vel;
        w_corr_za(s) = best_corr_w_za;
        w_corr_vel(s) = best_corr_w_vel;
        
    else
        % 数据质量不足，标记为无效
        ka_offsets_za(s) = NaN;
        ka_offsets_vel(s) = NaN;
        w_offsets_za(s) = NaN;
        w_offsets_vel(s) = NaN;
    end
end

%% 5. 计算平均校正值
fprintf('\n5. 计算平均校正值\n');

% 去除无效值
valid_ka_za = ~isnan(ka_offsets_za);
valid_ka_vel = ~isnan(ka_offsets_vel);
valid_w_za = ~isnan(w_offsets_za);
valid_w_vel = ~isnan(w_offsets_vel);

% 计算平均校正
mean_offset_ka_za = mean(ka_offsets_za(valid_ka_za));
mean_offset_ka_vel = mean(ka_offsets_vel(valid_ka_vel));
mean_offset_w_za = mean(w_offsets_za(valid_w_za));
mean_offset_w_vel = mean(w_offsets_vel(valid_w_vel));

% 计算标准差
std_offset_ka_za = std(ka_offsets_za(valid_ka_za));
std_offset_ka_vel = std(ka_offsets_vel(valid_ka_vel));
std_offset_w_za = std(w_offsets_za(valid_w_za));
std_offset_w_vel = std(w_offsets_vel(valid_w_vel));

fprintf('Ka波段校正统计 (基于%d个有效时刻):\n', sum(valid_ka_za));
fprintf('  反射率匹配: %.1f ± %.1f m\n', mean_offset_ka_za, std_offset_ka_za);
fprintf('  速度匹配: %.1f ± %.1f m\n', mean_offset_ka_vel, std_offset_ka_vel);

fprintf('\nW波段校正统计 (基于%d个有效时刻):\n', sum(valid_w_za));
fprintf('  反射率匹配: %.1f ± %.1f m\n', mean_offset_w_za, std_offset_w_za);
fprintf('  速度匹配: %.1f ± %.1f m\n', mean_offset_w_vel, std_offset_w_vel);

% 最终综合校正
final_offset_ka = (mean_offset_ka_za + mean_offset_ka_vel) / 2;
final_offset_w = (mean_offset_w_za + mean_offset_w_vel) / 2;

fprintf('\n最终平均校正建议:\n');
fprintf('  Ka波段: %.1f m\n', final_offset_ka);
fprintf('  W波段: %.1f m\n', final_offset_w);

%% 6. 可视化校正统计
fprintf('\n6. 创建校正统计可视化\n');

figure('Position', [100, 100, 1400, 1000]);

% 子图1: Ka波段校正分布
subplot(2,3,1)
histogram(ka_offsets_za(valid_ka_za), 20, 'FaceColor', 'g', 'FaceAlpha', 0.7)
hold on
xline(mean_offset_ka_za, 'r-', 'LineWidth', 3, 'DisplayName', sprintf('平均: %.1fm', mean_offset_ka_za))
xlabel('高度校正 [m]')
ylabel('频次')
title('Ka波段反射率校正分布')
legend
grid on

subplot(2,3,2)
histogram(ka_offsets_vel(valid_ka_vel), 20, 'FaceColor', 'g', 'FaceAlpha', 0.7)
hold on
xline(mean_offset_ka_vel, 'r-', 'LineWidth', 3, 'DisplayName', sprintf('平均: %.1fm', mean_offset_ka_vel))
xlabel('高度校正 [m]')
ylabel('频次')
title('Ka波段速度校正分布')
legend
grid on

% 子图2: W波段校正分布
subplot(2,3,3)
histogram(w_offsets_za(valid_w_za), 20, 'FaceColor', 'm', 'FaceAlpha', 0.7)
hold on
xline(mean_offset_w_za, 'r-', 'LineWidth', 3, 'DisplayName', sprintf('平均: %.1fm', mean_offset_w_za))
xlabel('高度校正 [m]')
ylabel('频次')
title('W波段反射率校正分布')
legend
grid on

subplot(2,3,4)
histogram(w_offsets_vel(valid_w_vel), 20, 'FaceColor', 'm', 'FaceAlpha', 0.7)
hold on
xline(mean_offset_w_vel, 'r-', 'LineWidth', 3, 'DisplayName', sprintf('平均: %.1fm', mean_offset_w_vel))
xlabel('高度校正 [m]')
ylabel('频次')
title('W波段速度校正分布')
legend
grid on

% 子图3: 校正时间序列
subplot(2,3,5)
sample_times = all_mrr_time(sample_indices(valid_ka_za));
plot(sample_times, ka_offsets_za(valid_ka_za), 'g.', 'MarkerSize', 8, 'DisplayName', 'Ka反射率')
hold on
plot(sample_times, ka_offsets_vel(valid_ka_vel), 'g^', 'MarkerSize', 6, 'DisplayName', 'Ka速度')
yline(mean_offset_ka_za, 'r-', 'LineWidth', 2)
yline(mean_offset_ka_vel, 'r--', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('校正偏移 [m]')
xlabel('时间 [UTC]')
title('Ka波段校正时间变化')
legend('Location', 'best')
grid on

subplot(2,3,6)
plot(sample_times, w_offsets_za(valid_w_za), 'm.', 'MarkerSize', 8, 'DisplayName', 'W反射率')
hold on
plot(sample_times, w_offsets_vel(valid_w_vel), 'm^', 'MarkerSize', 6, 'DisplayName', 'W速度')
yline(mean_offset_w_za, 'r-', 'LineWidth', 2)
yline(mean_offset_w_vel, 'r--', 'LineWidth', 2)
datetick('x', 'HH:MM')
ylabel('校正偏移 [m]')
xlabel('时间 [UTC]')
title('W波段校正时间变化')
legend('Location', 'best')
grid on

sgtitle('基于所有重合时刻的高度校正统计分析', 'FontSize', 14, 'FontWeight', 'bold')

% 保存统计图
print('height_correction_statistics', '-dpng', '-r300')
fprintf('   校正统计图已保存: height_correction_statistics.png\n');

%% 7. 校正稳定性分析
fprintf('\n7. 校正稳定性分析\n');

fprintf('校正稳定性评估:\n');
fprintf('  Ka波段反射率校正变异系数: %.1f%% (标准差/平均值)\n', ...
    std_offset_ka_za / abs(mean_offset_ka_za) * 100);
fprintf('  Ka波段速度校正变异系数: %.1f%%\n', ...
    std_offset_ka_vel / abs(mean_offset_ka_vel) * 100);
fprintf('  W波段反射率校正变异系数: %.1f%%\n', ...
    std_offset_w_za / abs(mean_offset_w_za) * 100);
fprintf('  W波段速度校正变异系数: %.1f%%\n', ...
    std_offset_w_vel / abs(mean_offset_w_vel) * 100);

% 计算平均相关系数
mean_corr_ka_za = mean(ka_corr_za(valid_ka_za));
mean_corr_ka_vel = mean(ka_corr_vel(valid_ka_vel));
mean_corr_w_za = mean(w_corr_za(valid_w_za));
mean_corr_w_vel = mean(w_corr_vel(valid_w_vel));

fprintf('\n平均相关系数:\n');
fprintf('  Ka波段反射率: %.3f\n', mean_corr_ka_za);
fprintf('  Ka波段速度: %.3f\n', mean_corr_ka_vel);
fprintf('  W波段反射率: %.3f\n', mean_corr_w_za);
fprintf('  W波段速度: %.3f\n', mean_corr_w_vel);

%% 8. 最终校正建议
fprintf('\n=== 最终校正建议 (基于所有重合时刻平均) ===\n');
fprintf('分析了%d个时间点的数据\n', n_samples);
fprintf('\n推荐高度校正:\n');
fprintf('  Ka波段高度 = 原始高度 %+.1f m\n', final_offset_ka);
fprintf('  W波段高度 = 原始高度 %+.1f m\n', final_offset_w);

fprintf('\n校正后高度范围:\n');
range_ka_corrected = range_ka_original + final_offset_ka;
range_w_corrected = range_ka_original + final_offset_w;
fprintf('  Ka波段: %.0f - %.0f m\n', min(range_ka_corrected), max(range_ka_corrected));
fprintf('  W波段: %.0f - %.0f m\n', min(range_w_corrected), max(range_w_corrected));

fprintf('\n校正可信度:\n');
if std_offset_ka_za / abs(mean_offset_ka_za) < 0.3
    fprintf('  Ka波段: 高 (变异系数 < 30%%)\n');
else
    fprintf('  Ka波段: 中等 (变异系数 ≥ 30%%)\n');
end

if std_offset_w_za / abs(mean_offset_w_za) < 0.3
    fprintf('  W波段: 高 (变异系数 < 30%%)\n');
else
    fprintf('  W波段: 中等 (变异系数 ≥ 30%%)\n');
end

fprintf('\n=== 基于所有时刻的高度校正分析完成 ===\n');
